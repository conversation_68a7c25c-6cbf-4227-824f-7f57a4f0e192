import { catchAll } from '../../utils/catchAll';
import { RequestHandler } from 'express';
import { patient, patients } from './mockData';
import { Admission, Dr, NoShows, PatientData, PatientOrder, PatientTreatmentPlan, ZohoPatientDb } from '../../types';
import { WebSocketManager } from '../../helpers/webSocketManager';
import { TOPICS } from '../../helpers/topics';
import { getFormatedZohoDate, getStrength, getYesterdayInTimeZone } from '../../utils/misc';
import { Queries } from '../../helpers/queries';
import { ZohoAuth, zohoLeadURL, zohoQueryURL } from '../../helpers/zoho';
import axios, { AxiosError } from 'axios';
import { db } from '../../utils/db';
import { ApiError } from '../../utils/ApiError';
import httpStatus from 'http-status';
import { sendEmail } from '../../helpers/zohoEmail';
import { logger } from '../../config/logger';
import { ApiResponse } from '../../helpers/response';
import { DateTime } from 'luxon';
import { verifyNoShowPatient } from '../common';
//import { startPatientReassignmentProcess } from './patientReassignment';
//import {  assignPatientToDoctor, getNextPatientsForDoctor } from './doctorQueueManager';
import {
  assignPatientToDoctor,
  assignPatientToDoctorForInDoctorQueue,
  calculateDynamicPatientLimit,
  getDoctorIdFromAccessId,
} from './doctorQueueManager';
import { getNextPatientsForDoctor } from './doctorQueueManager';
import { ZeldaManager } from '../../services/zeldaManager';
import { getPatientOnlineStatus, getBatchPatientOnlineStatus, getPatientQueueStatus } from '../../utils/patientStatus';

import config from '../../config';
import { PoolClient } from 'pg';

// Import production Zoho consultation fix
export { fixZohoConsultations, analyzeZohoConsultations } from './zohoConsultationFix';

export const timerMap = new Map();

//const noShowMap = new Map();
const timeZone = `AT TIME ZONE 'Australia/Sydney'`;
let notificationNumber: number | undefined = undefined;

let patientRedis: PatientData[] = [];
let lastCacheUpdateTime: string = new Date(0).toISOString(); // Initialize to epoch time in UTC ISO format

export const refreshPatientListFromZoho: RequestHandler = catchAll(async (_req, res) => {
  const yesterdayInAustralia = getYesterdayInTimeZone();
  const queryData = Queries.selectPatient(yesterdayInAustralia);
  const headers = await ZohoAuth.getHeaders();

  const response = await axios.post(zohoQueryURL, queryData, {
    headers,
  });
  res.status(200).send(response.data);
});

export const fetchPatientListFromZoho = async () => {
  const yesterdayInAustralia = getYesterdayInTimeZone();
  const queryData = Queries.selectPatient(yesterdayInAustralia);
  const headers = await ZohoAuth.getHeaders();

  const response = await axios.post(zohoQueryURL, queryData, {
    headers,
  });
  return response.data.data as ZohoPatientDb[];
};

export const postPatientsFromZoho = async () => {
  const patients: ZohoPatientDb[] = await fetchPatientListFromZoho();
  if (!Array.isArray(patients) || patients.length === 0) {
    return false;
  }

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Build placeholders and values for batch insert into Patient table
    const values: unknown[] = [];
    const placeholders: string[] = [];

    // Build placeholders and values for batch insert into Consultation table
    const consultationValues: unknown[] = [];
    const consultationPlaceholders: string[] = [];

    const questionnaireValues: unknown[] = [];
    const questionnairePlaceholders: string[] = [];

    let qOffset = 0;

    patients.forEach((patient, index) => {
      const {
        Full_Name,
        Email,
        Existing_Patient,
        Auto_Number_Member_ID,
        id,
        State,
        Consult_Range_Start,
        Consult_Range_End,
        Consult_Date_Time,
        Have_you_used_Alternative_Medicines_before_whethe,
        Date_of_Birth_2,
        Mobile,
        Risk_Rating,
        Condition,
        What_condition_or_symptom_are_you_having_issues_wi,
        Are_you_planning_to_have_children_in_the_near_futu,
        Do_you_suffer_from_any_cardiovascular_diseases_in,
        Do_you_have_an_addiction_to_any_psychoactive_subst,
        What_was_your_gender_at_birth,
        Please_add_the_first_medication_treatment_or_ther,
        Please_add_the_second_medication_treatment_or_the,
        Have_you_discussed_other_treatment_options_with_yo,
        Knowing_the_alternative_management_options_do_you,
        Do_you_suffer_from_psychosis_bipolar_disorder_or,
        Queue_Tags,
      } = patient;

      const lowerCaseEmail = Email?.toLowerCase();

      const offset = index * 10;
      placeholders.push(
        `($${offset + 1}, $${offset + 2}, $${offset + 3}, $${offset + 4}, $${offset + 5}, $${offset + 6}, $${offset + 7}, $${offset + 8}, $${offset + 9}, $${offset + 10}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      );

      const consultationOffset = index * 6;
      consultationPlaceholders.push(
        `($${consultationOffset + 1}, $${consultationOffset + 2}, $${consultationOffset + 3}, $${consultationOffset + 4}, $${consultationOffset + 5}, $${consultationOffset + 6}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
      );

      const questionnaireData = {
        Condition: Condition || 'No Answer',
        What_condition_or_symptom_are_you_having_issues_wi:
          What_condition_or_symptom_are_you_having_issues_wi || 'No Answer',
        Are_you_planning_to_have_children_in_the_near_futu:
          Are_you_planning_to_have_children_in_the_near_futu || 'No Answer',
        Do_you_suffer_from_any_cardiovascular_diseases_in:
          Do_you_suffer_from_any_cardiovascular_diseases_in || 'No Answer',
        Do_you_suffer_from_psychosis_bipolar_disorder_or:
          Do_you_suffer_from_psychosis_bipolar_disorder_or || 'No Answer',
        Do_you_have_an_addiction_to_any_psychoactive_subst:
          Do_you_have_an_addiction_to_any_psychoactive_subst || 'No Answer',
        What_was_your_gender_at_birth: What_was_your_gender_at_birth || 'No Answer',
        Please_add_the_first_medication_treatment_or_ther:
          Please_add_the_first_medication_treatment_or_ther || 'No Answer',
        Please_add_the_second_medication_treatment_or_the:
          Please_add_the_second_medication_treatment_or_the || 'No Answer',
        Have_you_discussed_other_treatment_options_with_yo:
          Have_you_discussed_other_treatment_options_with_yo || 'No Answer',
        Knowing_the_alternative_management_options_do_you:
          Knowing_the_alternative_management_options_do_you || 'No Answer',
      };

      Object.entries(questionnaireData).forEach(([question, answer]) => {
        if (answer) {
          questionnairePlaceholders.push(
            `($${qOffset + 1}, $${qOffset + 2}, $${qOffset + 3}, $${qOffset + 4}, $${qOffset + 5}, $${qOffset + 6}, $${qOffset + 7}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`,
          );

          questionnaireValues.push(
            Auto_Number_Member_ID, // patientID
            id, // zohoID
            lowerCaseEmail, // email
            question, // question text
            answer, // answer text
            'questionnaire', // type
            id, // Unique questionnaireID
          );

          qOffset += 7;
        }
      });

      values.push(
        Full_Name, // fullName
        lowerCaseEmail || null, // email
        Existing_Patient === 'Yes' ? true : false, // returningPatient
        Auto_Number_Member_ID, // patientID
        id, // zohoID
        State, // state
        Have_you_used_Alternative_Medicines_before_whethe === 'Yes' ? true : false,
        Date_of_Birth_2,
        Mobile,
        Risk_Rating === 'Low' ? 1 : Risk_Rating === 'Medium' ? 2 : Risk_Rating === 'High' ? 3 : 4,
      );

      consultationValues.push(
        Auto_Number_Member_ID, // patientID
        Consult_Range_Start, // consultationDate (extract only the date part)
        Consult_Date_Time, // consultationStart
        Consult_Range_End, // consultationEnd
        lowerCaseEmail,
        Queue_Tags ? Queue_Tags : 'pre-consult',
      );
    });

    const patientQuery = `
      INSERT INTO Patient (
        "fullName", email, "returningPatient", "patientID", "zohoID", state, "usedCannabisBefore", dob, mobile, "riskRating", "createdAt", "updatedAt"
      ) VALUES ${placeholders.join(', ')}
      ON CONFLICT ("email")
      DO UPDATE SET
        "fullName" = EXCLUDED."fullName",
        "patientID" = EXCLUDED."patientID",
        "returningPatient" = EXCLUDED."returningPatient",
        "zohoID" = EXCLUDED."zohoID",
        state = EXCLUDED.state,
        dob = EXCLUDED.dob,
        mobile = EXCLUDED.mobile,
        "riskRating" = EXCLUDED."riskRating",
        "usedCannabisBefore" = EXCLUDED."usedCannabisBefore",
        "updatedAt" = CURRENT_TIMESTAMP;
    `;

    const consultationQuery = `
      INSERT INTO Consultation (
        "patientID", "consultationStart", "consultationDate", "consultationEnd", email, "queueTag", "createdAt", "updatedAt"
      ) VALUES ${consultationPlaceholders.join(', ')}
      ON CONFLICT ("patientID", "consultationDate")
      DO UPDATE SET
        "consultationDate" = EXCLUDED."consultationDate",
        "consultationStart" = EXCLUDED."consultationStart",
        "consultationEnd" = EXCLUDED."consultationEnd",
        "queueTag" = CASE
                    WHEN Consultation."queueTag" IS NULL THEN EXCLUDED."queueTag"
                    ELSE Consultation."queueTag"
                 END,
        "updatedAt" = CURRENT_TIMESTAMP;
    `;

    const questionnaireQuery = `
      INSERT INTO Questionnaire (
        "patientID", "zohoID", "email", "question", "answers", "type", "questionnaireID", "createdAt", "updatedAt"
      ) VALUES ${questionnairePlaceholders.join(', ')}
      ON CONFLICT (email, "patientID", question)
      DO UPDATE SET
        "answers" = EXCLUDED."answers",
        "updatedAt" = CURRENT_TIMESTAMP;
    `;

    await client.query(patientQuery, values);
    await client.query(consultationQuery, consultationValues);
    await client.query(questionnaireQuery, questionnaireValues);

    await client.query('COMMIT');
    return true;
  } catch (e) {
    await client.query('ROLLBACK');
    console.error('Error inserting/updating patients:', e);
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
};

export const postPatient: RequestHandler = catchAll((req, res) => {
  const body = req.body;
  res.status(200).send(body);
});

export const postPatientDev: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const client = await db.connect();

  const patientData = {
    fullName: body.Full_Name,
    email: body.Email,
    returningPatient: body.Existing_Patient ? true : false,
    patientID: body.Auto_Number_Member_ID,
    zohoID: body.id,
  };

  const query = `
    INSERT INTO Patient ("fullName", email, "returningPatient", "patientID", "zohoID", "createdAt", "updatedAt")
    VALUES ($1, $2, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT (patientID)
    DO UPDATE SET
        "fullName" = EXCLUDED."fullName",
        email = EXCLUDED.email,
        "returningPatient" = EXCLUDED."returningPatient",
        "zohoID" = EXCLUDED."zohoID",
        "updatedAt" = CURRENT_TIMESTAMP;
  `;

  const values = [
    patientData.fullName,
    patientData.email,
    patientData.returningPatient,
    patientData.patientID,
    patientData.zohoID,
  ];

  try {
    await client.query('BEGIN');
    await client.query(query, values);
    res.status(200).send({ message: 'Patient record inserted or updated successfully.' });
    await client.query('COMMIT');
  } catch (error) {
    console.error('Error inserting/updating patient:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

// Helper function to check for new consultations
const checkForNewConsultations = async (): Promise<boolean> => {
  const client = await db.connect();
  try {
    const query = `
      SELECT COUNT(*) AS new_rows
      FROM Consultation
      WHERE "createdAt" > $1::timestamp
    `;
    const result = await client.query(query, [lastCacheUpdateTime]);
    return parseInt(result.rows[0].new_rows, 10) > 0;
  } catch (error) {
    console.error('Error checking for new consultations:', error);
    return true; // If there's an error, assume there are new consultations to be safe
  } finally {
    client.release();
  }
};

export const getPatientsRedis: RequestHandler = catchAll(async (_req, res) => {
  // Check if cache is empty or if there are new consultations
  if (patientRedis.length <= 0 || (await checkForNewConsultations())) {
    const client = await db.connect();

    const query = Queries.fetchPatientDataLightweight(); // Use lightweight query without patient history
    try {
      await client.query('BEGIN');
      await postPatientsFromZoho();
      const result = await client.query(query);
      await client.query('COMMIT');
      //update patient redis variable
      patientRedis = result.rows;
      lastCacheUpdateTime = new Date().toISOString(); // Update timestamp in UTC ISO format
      res.status(200).send(result.rows);
      return;
    } catch (error) {
      await client.query('ROLLBACK');
      logger.error(`Error inserting/updating patient`);
      console.error('Error inserting/updating patient:', error);
      const e = error as AxiosError;
      if (String(e.response?.status) === '401') {
        try {
          await ZohoAuth.forcedTokenRefresh();
          logger.info(`Forced token created...`);
          await client.query('BEGIN');
          await postPatientsFromZoho();
          const result = await client.query(query);
          await client.query('COMMIT');
          patientRedis = result.rows;
          lastCacheUpdateTime = new Date().toISOString(); // Update timestamp in UTC ISO format
          res.status(200).send(result.rows);
          return;
        } catch (error) {
          await client.query('ROLLBACK');
          console.error('Error inserting/updating patient:', error);
          res.status(401).send({ error: 'Unauthorized' });
          return;
        }
      } else {
        const result = await client.query(query);
        patientRedis = result.rows;
        lastCacheUpdateTime = new Date().toISOString(); // Update timestamp in UTC ISO format
        res.status(200).send(result.rows);
        return;
      }
    } finally {
      client.release();
    }
  }
  patientRedis = patientRedis.map((patient) => ({ ...patient, history: undefined }));
  res.status(200).send(patientRedis);
});

export const getPatients: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  // update Zoho and await for it
  // Decrypt Dr notes and send to UI
  // for (const patient of patients as PatientData[]){
  //     patient.history?.forEach(history => {
  //       history.drNotes = encryptionHelper.decrypt(history.drNotes)
  //     })
  // }

  const query = Queries.fetchPatientDataLightweight(); // Use lightweight query without patient history
  try {
    await client.query('BEGIN');
    await postPatientsFromZoho();
    const result = await client.query(query);
    await client.query('COMMIT');
    //update patient redis variable
    patientRedis = result.rows;

    res.status(200).send(result.rows);
    return;
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`Error inserting/updating patient`);
    console.error('Error inserting/updating patient:', error);
    const e = error as AxiosError;
    if (String(e.response?.status) === '401') {
      try {
        await ZohoAuth.forcedTokenRefresh();
        logger.info(`Forced token created...`);
        await client.query('BEGIN');
        await postPatientsFromZoho();
        const result = await client.query(query);
        await client.query('COMMIT');
        patientRedis = result.rows;
        res.status(200).send(result.rows);
        return;
      } catch (error) {
        await client.query('ROLLBACK');
        console.error('Error inserting/updating patient:', error);
        res.status(401).send({ error: 'Unauthorized' });
        return;
      }
    } else {
      const result = await client.query(query);
      res.status(200).send(result.rows);
      return;
    }
  } finally {
    client.release();
  }
});

export const postPatientData: RequestHandler = catchAll(async (req, res) => {
  const body = req.body as PatientTreatmentPlan;
  const patientID = body.patient?.patientID;
  const zohoID = body.patient?.zohoID;
  const consultationId = body.patient?.consultation?.id;
  const email = body.patient?.email;
  const client = await db.connect();

  await client.query('BEGIN');
  try {
    //   await client.query(
    //     `
    //   UPDATE Patient
    //   SET "drLocked" = $1, locked = $2 , "updatedAt" = CURRENT_TIMESTAMP
    //   WHERE "patientID" = $3
    // `,
    //     [null, false, patientID],
    //   );

    await client.query(
      `
    UPDATE Consultation
    SET "meetingOngoing" = $1, completed = $2,  "updatedAt" = CURRENT_TIMESTAMP
    WHERE "patientID" = $3
  `,
      [false, true, patientID],
    );

    await client.query(`UPDATE Patientqueue SET "noShow"=false WHERE "patientID"=$1`, [patientID]);
    // Ensure we always have a doctor name
    const drName = body.drName || '';

    // Check for existing treatment plan with the same patientID, consultationId, and email
    const checkDuplicateQuery = `
    SELECT id FROM TreatmentPlan
    WHERE "patientID" = $1
    AND "consultationId" = $2
    AND "email" = $3
    AND "outcome" = $4
    LIMIT 1
    `;

    const duplicateResult = await client.query(checkDuplicateQuery, [patientID, consultationId, email, body.outcome]);

    // If duplicate exists, log it and return early without updating Zoho
    if (duplicateResult.rows.length > 0) {
      logger.info(
        `Duplicate treatment plan detected for patientID: ${patientID}, consultationId: ${consultationId}, email: ${email}, outcome: ${body.outcome}`,
      );
      logger.info(`Skipping insertion of duplicate treatment plan and Zoho update`);

      await client.query('COMMIT');
      WebSocketManager.dispatch(TOPICS.REMOVE_ID, {
        patientID,
      });
      res.status(200).send({
        patientID,
        message: 'Treatment plan already exists',
      });
      return;
    }

    // MAIN OPERATION - Only insert if no duplicate exists
    const tretmentPlanQuery = `
    INSERT INTO TreatmentPlan (
    "patientID",
    "drId",
    "consultationId",
    "updatedAt",
    outcome,
    "drNotes",
    "diagnosis",
    "diagnosis",
    date,
    "drName",
    "strengthAndConcentration22",
    "dosePerDay22",
    "maxDose22",
    "totalQuantity22",
    "numberOfRepeat22",
    "supplyInterval22",
    "strengthAndConcentration29",
    "dosePerDay29",
    "maxDose29",
    "totalQuantity29",
    "numberOfRepeat29",
    "supplyInterval29",
    email,
    "mentalHealthSupprtingDocument",
    "idVerified",
    "idVerified",
    "type",
    "source",
    "source",
    "createdAt"
    )
    VALUES (
      $1, -- patientID
      $2, -- drId
      $3, -- consultationId
      CURRENT_TIMESTAMP, -- updatedAt
      $4, -- outcome
      $5, -- drNotes
      $6, -- diagnosis
      $7, -- date
      $8, -- drName
      $9, -- strengthAndConcentration
      $10, -- dosePerDay
      $11, -- maxDose
      $12, -- totalQuantity
      $13, -- numberOfRepeat
      $14, -- supplyInterval
      $6, -- diagnosis
      $7, -- date
      $8, -- drName
      $9, -- strengthAndConcentration
      $10, -- dosePerDay
      $11, -- maxDose
      $12, -- totalQuantity
      $13, -- numberOfRepeat
      $14, -- supplyInterval

      $15, -- strengthAndConcentration
      $16, -- dosePerDay
      $17, -- maxDose
      $18, -- totalQuantity
      $19, -- numberOfRepeat
      $20, -- supplyInterval
      $15, -- strengthAndConcentration
      $16, -- dosePerDay
      $17, -- maxDose
      $18, -- totalQuantity
      $19, -- numberOfRepeat
      $20, -- supplyInterval

      $21, -- email
      $22, -- mentalHealthSupprtingDocument
      $23, -- idVerified
      $21, -- email
      $22, -- mentalHealthSupprtingDocument
      $23, -- idVerified
      'treatmentplan',
      'consultation', -- source
      'consultation', -- source
      CURRENT_TIMESTAMP -- createdAt
    )
    `;

    await client.query(tretmentPlanQuery, [
      body.patient?.patientID,
      body.drId,
      body.patient?.consultation?.id,
      body.outcome,
      body.drNotes || '', // Use just the doctor notes without email data
      body.diagnosis, // Add diagnosis field
      body.diagnosis, // Add diagnosis field
      body.date,
      drName, // Use the doctor name with fallback
      body[22] ? '22%' : undefined,
      body[22]?.dosePerDay,
      body[22]?.maxDosePerDay,
      body[22]?.totalQuantity,
      body[22]?.numberOfRepeat,
      body[22]?.supplyInterval,
      body[29] ? '29%' : undefined,
      body[29]?.dosePerDay,
      body[29]?.maxDosePerDay,
      body[29]?.totalQuantity,
      body[29]?.numberOfRepeat,
      body[29]?.supplyInterval,
      body.patient?.email,
      body.mentalHealthSupportingDocumentation,
      body.idVerified,
      body.idVerified,
    ]);

    const headers = await ZohoAuth.getHeaders();
    const strainAdvice = [body?.email?.checkedSativa, body.email?.checkedIndica, body.email?.checkedHybrid]
      .flat()
      .filter((a) => a !== undefined)
      .reduce(
        (strainAdvice, value, index) => {
          strainAdvice[`strainAdvice${index + 1}`] = value;
          return strainAdvice;
        },
        {} as { [key: string]: string },
      );

    let otherTreatment: {
      [key: string]: string;
    } = {};

    if (body?.email?.otherTreatment) {
      otherTreatment = Object.keys(body.email.otherTreatment).reduce(
        (otherTreatment, key, index) => {
          if (body.email?.otherTreatment?.[key]) {
            otherTreatment[`OtherTreatment${index + 1}`] = body.email?.otherTreatment?.[key];
          }
          return otherTreatment;
        },
        {} as { [key: string]: string },
      );
    }

    // Get doctor initials from the database
    let doctorInitials = '';
    if (body.drId) {
      const doctorQuery = `SELECT initials FROM Dr WHERE "accessID" = $1`;
      const doctorResult = await client.query(doctorQuery, [body.drId]);
      if (doctorResult.rows.length > 0) {
        doctorInitials = doctorResult.rows[0].initials || '';
      }
    }

    // Check if the treatment plan contains "[CANCELLED BY DOCTOR]" in the intro message
    const isCancelledByDoctor = body?.email?.introMessage?.intro?.includes('[CANCELLED BY DOCTOR]');

    // Only send to Zoho if not cancelled by doctor
    if (!isCancelledByDoctor) {
      const data = {
        data: [
          {
            Dr_Approve_Date_Time: getFormatedZohoDate(),
            Strength_Concentration: getStrength(body),
            Dr_Trigger: body.outcome,
            Mental_Health_Supporting_Documentation: body.mentalHealthSupportingDocumentation,
            ID_Verified: body.idVerified,
            Consulting_Doctor: body.drName,
            Consulting_Doctor_Initials_1: doctorInitials,
            Prescription_Date_1: getFormatedZohoDate().split('T')[0],
            Specified_Dose: body[22]?.dosePerDay,
            Maximum_Doses_per_Day: body[22]?.maxDosePerDay,
            Total_Qty_22_1: body[22]?.totalQuantity,
            Number_of_Repeats_22: body[22]?.numberOfRepeat,
            Dose_Per_Day_29: body[29]?.dosePerDay,
            Maximum_Doses_per_Day_29: body[29]?.maxDosePerDay,
            Number_of_Repeats_29: body[22]?.numberOfRepeat ? body[22]?.numberOfRepeat : body[29]?.numberOfRepeat,
            Total_Qty_29_1: body[29]?.totalQuantity,
            Dispensing_Interval_Period_1: body[22]?.supplyInterval
              ? body[22]?.supplyInterval
              : body[29]?.supplyInterval,
            Doctor_Notes: body.drNotes !== undefined ? body.drNotes : '',
            Patient_Diagnosis: body.diagnosis !== undefined ? body.diagnosis : '',
            Dr_AHPRA_Number: body.drAphraNumber,
            Introduction1: body?.email?.introMessage?.intro,
            Introduction2: body?.email?.introMessage?.conclusion,
            SideEffect1: body?.email?.listTitle?.title1,
            SideEffect2: body?.email?.listTitle?.title2,
            SideEffect3: body?.email?.listTitle?.title3,
            SubSideEffect1: body?.email?.listItemText?.item1,
            SubSideEffect2: body?.email?.listItemText?.item2,
            SubSideEffect3: body?.email?.listItemText?.item3,
            ...strainAdvice,
            ...otherTreatment,
          },
        ],
      };

      const result = await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });
      if (result?.data?.data?.[0]) {
        if (result?.data?.data?.[0].status === 'error') {
          await client.query('ROLLBACK');
          throw new ApiError(httpStatus.BAD_REQUEST, result?.data?.data?.[0]);
        }

        if (result?.data?.data?.[0].status === 'success') {
          logger.info(`Successfully updated Patient :: ${zohoID} in Zoho`);
        }
      }
    } else {
      // Log that we're skipping the Zoho update because the treatment plan was cancelled
      logger.info(`Skipping Zoho update for patient ${patientID} - Treatment plan marked as cancelled by doctor`);
    }
    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.REMOVE_ID, {
      patientID,
    });
    res.status(200).send({
      patientID,
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// for later
export const postEmail: RequestHandler = catchAll((req, res) => {
  const body = req.body;

  // our API should be able to handle type below
  // app.use(express.text({ type: 'text/html' }));

  res.status(200).send(body);
});

export const postEmailTest: RequestHandler = catchAll(async (req, res) => {
  const body = req.body;
  const email = req.params.email;
  const leadID = req.params.id;

  /***
   * @param1 toEmail
   * @param2 leadID
   * @param3 Subject
   * @param4 content
   * @param5 FromEmail
   */

  const result = await sendEmail(
    email,
    leadID, // Lead ID
    'Treatment Plan',
    body,
  );
  res.status(200).send(result);
});

export const postRedirect: RequestHandler = catchAll((req, res) => {
  const patient = req.body;
  WebSocketManager.dispatch(TOPICS.ADMITTED_PATIENT, patient);
  res.status(200).send(patient);
});

interface UpdateLockStatusBody {
  status: boolean;
  dr: string;
}

export const updatePatientLockStatus: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const { status, dr } = req.body as UpdateLockStatusBody;
  const client = await db.connect();

  try {
    // Input validation
    if (!patientID) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
    }

    if (typeof status !== 'boolean') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Status must be a boolean value');
    }

    if (!dr || typeof dr !== 'string') {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Doctor ID is required and must be a string');
    }

    // Check if patient exists
    const patientCheckQuery = `SELECT "patientID", "zohoID" FROM Patient WHERE "patientID" = $1`;
    const patientExists = await client.query(patientCheckQuery, [patientID]);

    if (patientExists.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, `Patient with ID ${patientID} not found`);
    }

    await client.query('BEGIN');

    // Get patient's zohoID for booking lookup
    //const zohoID = patientExists.rows[0].zohoID;

    // Find the doctor this patient is actually booked with
    const findBookedDoctorQuery = `
      SELECT d."accessID" as booked_doctor_id
      FROM Patient p
      JOIN patientslot ps ON p."zohoID" = ps.patient_id
      JOIN range r ON ps.range_id = r.id
      JOIN dr d ON r."doctorID" = d.id
      WHERE p."patientID" = $1
      AND r.date::DATE >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND r.date::DATE < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      LIMIT 1
    `;

    const bookedDoctorResult = await client.query(findBookedDoctorQuery, [patientID]);

    // Determine which doctor ID to use
    let effectiveDoctorID = dr; // Default to requested doctor

    if (bookedDoctorResult.rows.length > 0) {
      // Patient has a booking with a doctor today
      const bookedDoctorID = bookedDoctorResult.rows[0].booked_doctor_id;

      if (bookedDoctorID !== dr) {
        // Log that we're using the booked doctor instead of requested doctor
        logger.info(
          `Patient ${patientID} was being assigned to doctor ${dr}, but is actually booked with doctor ${bookedDoctorID}. Using booked doctor ID.`,
        );
        effectiveDoctorID = bookedDoctorID;
      }
    }

    // For backward compatibility, still update the Patient table
    // const patientQuery = `
    //   UPDATE Patient
    //   SET locked = $1,
    //       "drLocked" = $2::text
    //   WHERE "patientID" = $3
    //   RETURNING "patientID", locked, "drLocked"
    // `;
    // const patientResult = await client.query(patientQuery, [status, effectiveDoctorID, patientID]);

    // if (patientResult.rows.length === 0) {
    //   throw new ApiError(httpStatus.NOT_FOUND, `Failed to update patient lock status`);
    // }

    // Update the PatientQueue table with assignedDoctorID
    // Only update assignedDoctorID if locking; never set to NULL
    const queueQuery = `
      UPDATE PatientQueue
      SET
        "assignedDoctorID" = CASE
          WHEN $1::boolean = true THEN $2::text
          ELSE "assignedDoctorID"
        END,
        "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $3
      RETURNING "patientID", "assignedDoctorID"
    `;

    const queueResult = await client.query(queueQuery, [status, effectiveDoctorID, patientID]);

    if (queueResult.rows.length === 0) {
      //logger.warn(`No PatientQueue record found for patientID: ${patientID}`);
    }

    await client.query('COMMIT');

    // Combine the results
    const response = {
      // ...patientResult.rows[0],
      assignedDoctorID: status ? effectiveDoctorID : null,
      success: true,
      message: `Patient ${status ? 'locked' : 'unlocked'} successfully`,
    };

    //WebSocketManager.dispatch(TOPICS.UPDATE_LOCK_STATUS, response);
    res.status(200).send(response);
  } catch (e) {
    await client.query('ROLLBACK');

    if (e instanceof ApiError) {
      throw e;
    }

    const error = e as Error;
    logger.error('Error updating patient lock status:', {
      error: error.message,
      patientID,
      status,
      dr,
    });
    throw new ApiError(httpStatus.BAD_REQUEST, `Failed to update patient lock status: ${error.message}`);
  } finally {
    client.release();
  }
});

export const verifyLockedPatient: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.params.id;

  // Query both Patient and PatientQueue tables
  const query = `
    SELECT
      p."drLocked",
      p.locked,
      pq."assignedDoctorID"
    FROM
      Patient p
    LEFT JOIN
      PatientQueue pq ON p."patientID" = pq."patientID"
    WHERE
      p."patientID" = $1
  `;

  try {
    const result = await client.query(query, [patientID]);

    if (result.rows.length) {
      // Prioritize assignedDoctorID over drLocked
      const response = {
        drLocked: result.rows[0].drLocked,
        locked: result.rows[0].locked,
        assignedDoctorID: result.rows[0].assignedDoctorID,
      };
      res.status(200).send(response);
    } else {
      res.status(200).send(undefined);
    }
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const postDoctorData: RequestHandler = catchAll(async (req, res) => {
  const doctorData = req.body as Dr;
  const client = await db.connect();

  const query = `
  INSERT INTO Dr (email, name, username, "accessID", status, role, "aphraNumber", "consultationDurationMinutes", "createdAt", "updatedAt") VALUES($1, $2, $3, $4, $5, $6, $7, $8, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  ON CONFLICT (email)
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      name = EXCLUDED.name,
      username = EXCLUDED.username,
      status = EXCLUDED.status,
      role = EXCLUDED.role,
      "aphraNumber" = EXCLUDED."aphraNumber",
      "consultationDurationMinutes" = EXCLUDED."consultationDurationMinutes"
  `;

  try {
    await client.query('BEGIN');
    const { rows } = await client.query(query, [
      doctorData.email,
      doctorData.name,
      doctorData.username,
      doctorData.accessID,
      'active',
      doctorData.role,
      doctorData.aphraNumber,
      doctorData.consultationDurationMinutes || 6,
    ]);
    res.status(200).send(rows[0]);
    await client.query('COMMIT');
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getDoctorById: RequestHandler = catchAll(async (req, res) => {
  const id = req.params.id;
  const client = await db.connect();
  const query = `SELECT * FROM Dr WHERE "accessID" =$1;`;
  try {
    const { rows } = await client.query(query, [id]);

    res.cookie('dr_id', rows?.[0].accessID, {
      httpOnly: true,
      secure: true,
    });

    res.status(200).send(rows[0]);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getDoctors: RequestHandler = catchAll(async (_req, res) => {
  // req.log?.info('This is just trying');
  const client = await db.connect();
  const query = `SELECT * FROM Dr;`;
  try {
    const { rows } = await client.query(query);
    res.status(200).send(rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getActiveDoctors: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  // Modified query to return username as name
  const query = `
    SELECT 
      id, 
      "createdAt", 
      "updatedAt", 
      "consultationDurationMinutes", 
      "accessID", 
      status, 
      role, 
      "aphraNumber", 
      email, 
      username as name, 
      username 
    FROM Dr 
    WHERE status = 'active' AND role = 'doctor';
  `;
  try {
    const { rows } = await client.query(query);
    res.status(200).send(rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const sendPatientToDoctor: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.body.patientID as string;

  try {
    await client.query('BEGIN');

    // Get patient's zohoID
    const patientQuery = `SELECT "zohoID", "fullName" FROM Patient WHERE "patientID" = $1`;
    const patientResult = await client.query(patientQuery, [patientID]);

    if (patientResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(404).send({ error: 'Patient not found.' });
      return;
    }

    const patientZohoID = patientResult.rows[0].zohoID;
    const patientName = patientResult.rows[0].fullName;

    // Find the doctor assigned to this patient through patientslot and range
    const findDoctorQuery = `
      SELECT d.id as doctor_db_id, d."accessID" as doctor_access_id, d.username as doctor_name
      FROM patientslot ps
      JOIN range r ON ps.range_id = r.id
      JOIN dr d ON r."doctorID" = d.id
      WHERE ps.patient_id = $1
    `;

    const doctorResult = await client.query(findDoctorQuery, [patientZohoID]);

    if (doctorResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(400).send({ error: 'No doctor assigned to this patient. Please assign a doctor first.' });
      return;
    }

    const doctorAccessID = doctorResult.rows[0].doctor_access_id;
    const doctorName = doctorResult.rows[0].doctor_name;

    const updateQueue = `
    WITH upserted AS (
      INSERT INTO PatientQueue ("patientID", status, "notificationSent", "notificationSentDateTime", "updatedAt", "assignedDoctorID")
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $4)
      ON CONFLICT ("patientID")
      DO UPDATE SET
        "updatedAt" = CURRENT_TIMESTAMP,
        "notificationSent" = EXCLUDED."notificationSent",
        "notificationSentDateTime" = CURRENT_TIMESTAMP,
        "assignedDoctorID" = EXCLUDED."assignedDoctorID"
  RETURNING
        "patientID",
        "status",
        TO_CHAR(("createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        TO_CHAR(("updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
        TO_CHAR(("joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
        "notificationSent",
        TO_CHAR(("notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
        TO_CHAR(("leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
        TO_CHAR(("joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
        TO_CHAR(("admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
        TO_CHAR(("completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
        TO_CHAR(("callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
        TO_CHAR(("confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
        "assignedDoctorID"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
  `;

    const updateConsultation = `
      UPDATE Consultation
      SET
        "notificationSent" = $1,
        "notificationSentDateTime" = CURRENT_TIMESTAMP,
        "updatedAt" = CURRENT_TIMESTAMP
      WHERE id = $2
      RETURNING
      "patientID",
      "drId",
      "drJoined",
      ("updatedAt"  ${timeZone}) AS "updatedAt",
      ("joinedAt"  ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime"  ${timeZone}) AS "notificationSentDateTime",
      "meetingOngoing",
      "completed";`;

    // Get the consultation for this patient
    const targetPatient = await client.query(Queries.fetchNextPatientByPatientID(), [patientID]);

    if (targetPatient.rows.length > 0) {
      logger.info(`Updating Consultation by Admin :: ${targetPatient.rows[0]?.consultation?.id}`);

      // Send notification via Zoho
      const headers = await ZohoAuth.getHeaders();
      const data = {
        data: [
          {
            Send_Notification: 'yes',
          },
        ],
      };

      await axios.put(`${zohoLeadURL}/${patientZohoID}`, data, { headers });

      // Update consultation
      const { rows } = await client.query(updateConsultation, [true, targetPatient.rows[0]?.consultation?.id]);
      logger.info(`Updated Consultation :: ${targetPatient.rows[0]?.consultation?.id}`);
      WebSocketManager.dispatch(TOPICS.UPDATE_CONSULTATION, rows[0]);

      // Ensure DoctorQueue and PatientQueue are in sync
      await assignPatientToDoctor(db, patientID, doctorAccessID, client);
      // Always assign doctorID
      const result = await client.query(updateQueue, [patientID, 'ONLINE', true, doctorAccessID]);

      await client.query('COMMIT');
      logger.info(`Patient ${patientName} has been notified by Admin and assigned to doctor ${doctorName}`);

      WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, result?.rows?.[0]);

      // Only call warnPatient if a patient was actually notified
      if (result?.rows?.length > 0) {
        logger.info(`Patient was notified by Admin, proceeding with warning system`);
        await warnPatient();
      }

      res.status(200).send(result?.rows?.[0]);
      return;
    }

    await client.query('ROLLBACK');
    res.status(200).send([]);
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updateMeetingStatus: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const status = req.body.status;
  const client = await db.connect();

  // if user has not been notified, Dr will not see them online.
  // if user is not qualified do not show them online e.g. no show user

  const consultation = `
    WITH LatestConsultation AS (
      SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
      FROM Consultation c
      WHERE c."patientID" = $1
      ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC  -- Get the most recent consultation per patient
  )
  SELECT lc.id, lc."patientID"
  FROM LatestConsultation lc
  WHERE
      lc."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND lc."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND lc.completed = FALSE
      AND lc."notificationSent" = TRUE
  LIMIT 1;`;

  const updateConsultation = `
  UPDATE Consultation
  SET
    "meetingOngoing" = $1,
    "joinedAt" = CURRENT_TIMESTAMP,
    "updatedAt" = CURRENT_TIMESTAMP
  WHERE id = $2
  RETURNING
    "patientID",
    "drId",
    "drJoined",
    TO_CHAR(("consultationDate" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
    TO_CHAR(("createdAt" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
    TO_CHAR(("updatedAt" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
    TO_CHAR(("joinedAt" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
    TO_CHAR(("consultationStart" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationStart",
    TO_CHAR(("consultationEnd" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationEnd",
    TO_CHAR(("notificationSentDateTime" AT TIME ZONE 'Australia/Sydney'), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
    "notificationSent",
    "meetingOngoing",
    "completed";`;

  try {
    // Check if this is Zelda test patient - prevent her from going online
    if (status === true && config.zelda?.enabled && config.zelda?.patientEmail) {
      try {
        const patientCheck = await client.query(`SELECT email, "fullName" FROM Patient WHERE "patientID" = $1`, [
          patientID,
        ]);

        if (patientCheck.rows.length > 0 && patientCheck.rows[0].email === config.zelda.patientEmail) {
          logger.info(
            `Zelda test patient (${config.zelda.patientEmail}) attempted to update meeting status to online - blocked`,
          );
          res.status(200).send([]);
          return;
        }
      } catch (error) {
        logger.error('Error checking if patient is Zelda in updateMeetingStatus:', error);
        // Continue with normal flow if check fails
      }
    }
    await client.query('BEGIN');
    const existingConsultation = await client.query(consultation, [patientID]);
    if (existingConsultation.rows.length > 0) {
      const { rows } = await client.query(updateConsultation, [status, existingConsultation.rows?.[0]?.id]);
      await client.query('COMMIT');
      WebSocketManager.dispatch(TOPICS.UPDATE_CONSULTATION, rows[0]);
      res.status(200).send([rows[0]]);
      return;
    } else {
      res.status(200).send([]);
      return;
    }
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updateNextPatient: RequestHandler = catchAll((_req, res) => {
  // Notify DB End Point to send link to the next patient with the Zoho ID

  res.status(200).send(patient);
});

export const notifyNextPatient: RequestHandler = catchAll(async (req, res) => {
  // Notify Zoho End Point to send link to the this patient using zohoID

  // add them to queue with status OFFLINE
  const { doctorId } = req.body; // Accept doctorId from request body
  const client = await db.connect();

  const updateConsultation = `
    UPDATE Consultation
    SET
      "notificationSent" = $1,
      "notificationSentDateTime" = CURRENT_TIMESTAMP,
      "updatedAt" = CURRENT_TIMESTAMP
    WHERE id = $2
    RETURNING
    "patientID",
    "drId",
    "drJoined",
    ("updatedAt"  ${timeZone}) AS "updatedAt",
    ("joinedAt"  ${timeZone}) AS "joinedAt",
    "notificationSent",
    ("notificationSentDateTime"  ${timeZone}) AS "notificationSentDateTime",
    "meetingOngoing",
    "completed";`;

  const updateQueue = `
    WITH upserted AS (
      INSERT INTO PatientQueue ("patientID", "status", "email", "createdAt", "updatedAt", "notificationSentDateTime", "notificationSent")
      VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true)
      ON CONFLICT ("patientID")
      DO UPDATE SET
        "updatedAt" = CURRENT_TIMESTAMP,
        "notificationSentDateTime" = CURRENT_TIMESTAMP,
        "notificationSent" = EXCLUDED."notificationSent",
        "status" = EXCLUDED."status"
      RETURNING
            "patientID",
            "status",
            ("createdAt" ${timeZone}) AS "createdAt",
            ("updatedAt" ${timeZone}) AS "updatedAt",
            ("joinedAt" ${timeZone}) AS "joinedAt",
            "notificationSent",
            ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
            ("leftAt" ${timeZone}) AS "leftAt",
            ("completedAt" ${timeZone}) AS "completedAt",
            ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
            ("admittedAt" ${timeZone}) AS "admittedAt"
        )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
    `;

  try {
    await client.query('BEGIN');

    // Get doctor's ID from accessID
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [doctorId]);

    if (doctorResult.rows.length === 0) {
      logger.error(`Doctor with accessID ${doctorId} not found`);
      await client.query('ROLLBACK');
      res.status(404).send({ message: 'Doctor not found' });
      return;
    }

    const doctorName = doctorResult.rows[0].username;
    const doctorDbId = doctorResult.rows[0].id;

    // Get next patient for this specific doctor based on patientslot and range assignment
    const nextPatientQuery = `
      WITH LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id as consultation_id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND (c."notificationSent" = false OR c."notificationSent" IS NULL)
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      )
      SELECT 
        p."patientID",
        p."fullName",
        p.email,
        p."zohoID",
        lc.consultation_id,
        lc."consultationDate"
      FROM Patient p
      JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
      JOIN patientslot ps ON p."zohoID" = ps.patient_id
      JOIN range r ON ps.range_id = r.id
      WHERE lc.completed = false
      AND r."doctorID" = $1
      ORDER BY lc."consultationDate" ASC NULLS LAST, p."returningPatient" DESC, p."riskRating" ASC NULLS LAST
      LIMIT 1;
    `;

    const gettingNextPatient = await client.query(nextPatientQuery, [doctorDbId]);

    if (gettingNextPatient.rows.length > 0) {
      const nextPatient = gettingNextPatient.rows[0];
      logger.info(`Updating Consultation :: ${nextPatient.consultation_id}`);
      const patientID = nextPatient.patientID;
      const email = nextPatient.email;
      const zohoID = nextPatient.zohoID;

      const headers = await ZohoAuth.getHeaders();

      const data = {
        data: [
          {
            Send_Notification: 'yes',
          },
        ],
      };

      await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });

      const { rows } = await client.query(updateConsultation, [true, nextPatient.consultation_id]);
      logger.info(`Updated Consultation :: ${nextPatient.consultation_id}`);
      WebSocketManager.dispatch(TOPICS.UPDATE_CONSULTATION, rows[0]);

      const result = await client.query(updateQueue, [patientID, 'OFFLINE', email]);
      WebSocketManager.dispatch(TOPICS.ADD_PATIENT_QUEUE, result?.rows?.[0]);

      // Assign patient to doctor
      if (doctorId && patientID) {
        await assignPatientToDoctor(db, patientID, doctorId, client);
      }

      await client.query('COMMIT');
      logger.info(`Patient ${nextPatient.fullName} has been notified and assigned to doctor ${doctorName}`);

      // Only call warnPatient if a patient was actually notified
      if (result?.rows?.length > 0) {
        logger.info(`Patient was notified directly, proceeding with warning system`);
        await warnPatient();
      } else {
        logger.info(`No patient was notified directly, skipping warning system`);
      }

      res.status(200).send(result?.rows?.[0]);
      return;
    }

    // If no patients found for this doctor through patientslot and range, log it
    logger.info(`No patients found for doctor ${doctorName} through patientslot and range assignment`);
    await client.query('ROLLBACK');
    res.status(200).send([]);
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// Extended PatientData type to include consultationId
interface ExtendedPatientData extends PatientData {
  consultationId?: string;
}

export const notifyNextPatientMiddleWare = async (doctorID: string) => {
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Get doctor details
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [doctorID]);

    if (doctorResult.rows.length === 0) {
      logger.info(`Doctor with ID ${doctorID} not found`);
      await client.query('ROLLBACK');
      return [];
    }

    // Use per-doctor calculation instead of global calculation for independent queue system
    // Each doctor's batch size should be based on their own unnotified patients
    let batchSize = await calculateDynamicPatientLimit(db, doctorID);

    // Use the doctor name from the earlier query
    const doctorName = doctorResult.rows[0]?.username || doctorID;

    logger.info(`Doctor ${doctorName} - Calculated batch size: ${batchSize} (based on their own unnotified patients)`);

    // Single doctor throttling: limit batch size to max 3 to avoid overwhelming single doctors
    const activeDoctorCount = Array.from(timerMap.keys()).filter((key) => key !== 'patientInterval').length;
    if (activeDoctorCount === 1) {
      const originalBatchSize = batchSize;
      batchSize = Math.min(batchSize, 3);
      logger.info(
        `Single doctor throttling applied - Doctor: ${doctorName}, Original batch: ${originalBatchSize}, Limited to: ${batchSize}`,
      );
    }

    // Get next patients for this doctor using the notification batch size
    const patientIDs = await getNextPatientsForDoctor(db, doctorID, batchSize);
    //logger.info(`Batch patientIDs to notify: ${JSON.stringify(patientIDs)}`);

    // Convert doctorID (accessID) to actual doctor ID for the no-show patient query
    const actualDoctorID = await getDoctorIdFromAccessId(db, doctorID);
    if (actualDoctorID) {
      // Get one no-show patient to notify - now filtered by doctor assignment
      const gettingNextNoShowPatient = await client.query(Queries.fetchNextNoShowPatientForDoctor(), [actualDoctorID]);
      if (gettingNextNoShowPatient.rows.length > 0) {
        const noShowPatientID = gettingNextNoShowPatient.rows[0].patientID;
        if (!patientIDs.includes(noShowPatientID)) {
          patientIDs.push(noShowPatientID);
          logger.info(
            `Including no-show patient ${gettingNextNoShowPatient.rows[0].fullName} assigned to doctor ${doctorName} in notifications`,
          );
        }
      }
    } else {
      logger.error(`Could not find doctor ID for accessID ${doctorID} when fetching no-show patients`);
    }

    if (!patientIDs || patientIDs.length === 0) {
      //logger.info(`No available patients to notify for doctor ${doctorName}`);
      await client.query('ROLLBACK');
      return [];
    }

    const notifiedPatients: ExtendedPatientData[] = [];

    for (const patientID of patientIDs) {
      // Atomically claim the consultation for this patient
      const claimConsultationQuery = `
        UPDATE Consultation
        SET "notificationSent" = true,
            "notificationSentDateTime" = CURRENT_TIMESTAMP
        WHERE "patientID" = $1
          AND ("notificationSent" = false OR "notificationSent" IS NULL)
          AND completed = false
          AND "consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
          AND "consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        RETURNING *;
      `;
      const claimResult = await client.query(claimConsultationQuery, [patientID]);
      if (claimResult.rows.length === 0) {
        logger.info(`Patient ${patientID} already notified or not eligible, skipping.`);
        continue; // Already claimed by another doctor
      }
      const claimedConsultation = claimResult.rows[0];
      //logger.info(`Doctor ${doctorID} claimed consultation ${claimedConsultation.id} for notification.`);

      // Fetch patient details
      const patientDetailsQuery = `
        SELECT p.*, c.id as "consultationId", c.*
        FROM Patient p
        JOIN Consultation c ON p."patientID" = c."patientID"
        WHERE c.id = $1
        LIMIT 1
      `;
      const patientDetailsResult = await client.query(patientDetailsQuery, [claimedConsultation.id]);
      if (patientDetailsResult.rows.length === 0) {
        logger.info(`No patient found for claimed consultation ${claimedConsultation.id}`);
        continue;
      }
      const nextPatient = patientDetailsResult.rows[0] as ExtendedPatientData;

      // Get doctor name for logging
      const doctorQuery = Queries.getDoctorByAccessID();
      const doctorResult = await client.query(doctorQuery, [doctorID]);
      const doctorName = doctorResult.rows[0]?.username || doctorID;

      // Notify Zoho
      const headers = await ZohoAuth.getHeaders();
      const zohoID = await client.query(`SELECT "zohoID" FROM Patient WHERE "patientID" = $1`, [nextPatient.patientID]);
      const data = {
        data: [
          {
            Send_Notification: 'yes',
            Consulting_Doctor: doctorName,
          },
        ],
      };
      await axios.put(`${zohoLeadURL}/${zohoID?.rows?.[0].zohoID}`, data, { headers });

      // Insert/Update PatientQueue with proper timezone handling
      const upsertQueueQuery = `
        WITH upserted AS (
          INSERT INTO PatientQueue ("patientID", "status", email, "createdAt", "updatedAt", "notificationSentDateTime", "notificationSent", "assignedDoctorID", "consultationId")
          VALUES ($1, 'OFFLINE', $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, true, $3, $4)
          ON CONFLICT ("patientID")
          DO UPDATE SET
            "updatedAt" = CURRENT_TIMESTAMP,
            "notificationSentDateTime" = CURRENT_TIMESTAMP,
            "notificationSent" = EXCLUDED."notificationSent",
            "status" = EXCLUDED."status",
            "assignedDoctorID" = EXCLUDED."assignedDoctorID",
            "consultationId" = EXCLUDED."consultationId"
          RETURNING
            "patientID",
            "status",
            ("createdAt" ${timeZone}) AS "createdAt",
            ("updatedAt" ${timeZone}) AS "updatedAt",
            ("joinedAt" ${timeZone}) AS "joinedAt",
            "notificationSent",
            ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
            ("leftAt" ${timeZone}) AS "leftAt",
            ("completedAt" ${timeZone}) AS "completedAt",
            ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
            ("admittedAt" ${timeZone}) AS "admittedAt",
            "assignedDoctorID",
            "consultationId"
        )
        SELECT
          u.*,
          p."fullName"
        FROM upserted u
        JOIN Patient p ON u."patientID" = p."patientID";
      `;
      const queueResult = await client.query(upsertQueueQuery, [
        nextPatient.patientID,
        nextPatient.email,
        doctorID,
        nextPatient.consultationId,
      ]);
      WebSocketManager.dispatch(TOPICS.ADD_PATIENT_QUEUE, queueResult.rows[0]);

      // Assign patient to doctor in DoctorQueue
      await assignPatientToDoctorForInDoctorQueue(db, nextPatient.patientID, doctorID, client);

      logger.info(`Patient ${nextPatient.fullName} has been notified by doctor ${doctorName}`);

      notifiedPatients.push(nextPatient);
    }

    await client.query('COMMIT');
    return notifiedPatients;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    logger.error(`Error in notifyNextPatientMiddleWare: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
};

// create a handler to deal with this use case.
// carefully query the count for the patient. Do not just get it anyhow

export const checkNoShow: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.body.patientID;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "status", email, "createdAt", "updatedAt", "joinedAt", "firstTimeJoined")
    VALUES ($1,  $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "joinedAt" = CURRENT_TIMESTAMP,
      "firstTimeJoined" = CASE WHEN PatientQueue."firstTimeJoined" IS NULL THEN CURRENT_TIMESTAMP ELSE PatientQueue."firstTimeJoined" END,
      "status" = EXCLUDED."status"
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt"
    )
    SELECT
        u.*,
        p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
    `;

  const patientQueueDetails = `
    WITH upserted AS (
      INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "joinedAt", "status", email)
      VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3)
      RETURNING
        "patientID",
        "status",
        ("createdAt" ${timeZone}) AS "createdAt",
        ("updatedAt" ${timeZone}) AS "updatedAt",
        ("joinedAt" ${timeZone}) AS "joinedAt",
        "notificationSent",
        ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
        ("leftAt" ${timeZone}) AS "leftAt",
        ("completedAt" ${timeZone}) AS "completedAt",
        ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
        ("admittedAt" ${timeZone}) AS "admittedAt"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
  `;

  try {
    await client.query('BEGIN');

    const allowedPatient = await client.query(`SELECT allowed FROM PatientQueue WHERE "patientID"=$1 AND allowed=$2`, [
      patientID,
      true,
    ]);
    if (allowedPatient && allowedPatient.rows.length > 0 && allowedPatient.rows[0].allowed) {
      await client.query('COMMIT');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT IS NOT A NO-SHOW', [], true));
      return;
    }

    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    const noShowCount = patient?.email && (await verifyNoShowPatient(patient.email));

    // if Patient has been no show for more than 3 times
    if (noShowCount && noShowCount.length > 4) {
      const { rows } = await client.query(query, [patientID, 'NO-SHOW', patient?.email]);
      await client.query(patientQueueDetails, [patientID, 'NO-SHOW', patient?.email]);
      await client.query('COMMIT');
      WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
      logger.info(`Updated ${patientID} with Status NO-SHOW`);
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESSFULLY UPDATED PATIENT QUEUE', rows, true));
      return;
    }

    await client.query('COMMIT');
    // logger.info(`Updated ${patientID} with Status ONLINE`);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT IS NOT A NO-SHOW', [], true));
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const postPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  // they should already existing in queue table,
  // status to ONLINE

  const client = await db.connect();
  const patientID = req.body.patientID;

  const consultation = `
  WITH LatestConsultation AS (
    SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
    FROM Consultation c
    WHERE c."patientID" = $1
    ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation per patient
)
  SELECT lc.id, lc."patientID"
  FROM LatestConsultation lc
  WHERE
      lc."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND lc."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND lc.completed = FALSE
      AND lc."notificationSent" = TRUE
  LIMIT 1;`;

  const updateConsultationTag = `UPDATE Consultation SET "queueTag" = 'showed-up' WHERE id = $1`;

  // Query to find the doctor assigned to this patient through patientslot and range
  const findDoctorQuery = `
    SELECT d."accessID" as doctor_access_id
    FROM Patient p
    JOIN patientslot ps ON p."zohoID" = ps.patient_id
    JOIN range r ON ps.range_id = r.id
    JOIN dr d ON r."doctorID" = d.id
    WHERE p."patientID" = $1
    AND r.date::DATE >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
    AND r.date::DATE < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
    LIMIT 1
  `;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "allowed", "joinedAt", "firstTimeJoined", "assignedDoctorID", "consultationId")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5, $6)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "joinedAt" = COALESCE(PatientQueue."joinedAt", CURRENT_TIMESTAMP), 
      "firstTimeJoined" = COALESCE(PatientQueue."firstTimeJoined", CURRENT_TIMESTAMP),
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID"),
      "consultationId" = EXCLUDED."consultationId"
RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      "allowed",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
    )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "allowed", "joinedAt", "firstTimeJoined", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $5)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "joinedAt" = COALESCE(PatientQueue."joinedAt", CURRENT_TIMESTAMP), 
      "firstTimeJoined" = COALESCE(PatientQueue."firstTimeJoined", CURRENT_TIMESTAMP),
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      "allowed",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID"
    )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
  WITH upserted AS (
    INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email)
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3)
  RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt"
    )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
  `;

  try {
    // Check if this is Zelda test patient - prevent her from going online
    if (config.zelda?.enabled && config.zelda?.patientEmail) {
      try {
        const patientCheck = await client.query(`SELECT email, "fullName" FROM Patient WHERE "patientID" = $1`, [
          patientID,
        ]);

        if (patientCheck.rows.length > 0 && patientCheck.rows[0].email === config.zelda.patientEmail) {
          logger.info(`Zelda test patient (${config.zelda.patientEmail}) attempted to go online - blocked`);
          res.status(200).send({ message: 'Test patient cannot go online' });
          return;
        }
      } catch (error) {
        logger.error('Error checking if patient is Zelda:', error);
        // Continue with normal flow if check fails
      }
    }

    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    const consultationResult = await client.query(consultation, [patientID]);

    interface PatientQueueRow {
      patientID: string;
      status: string;
      createdAt: string;
      updatedAt: string;
      joinedAt: string | null;
      notificationSent: boolean | null;
      notificationSentDateTime: string | null;
      leftAt: string | null;
      completedAt: string | null;
      joinedCallAt: string | null;
      admittedAt: string | null;
      confirmedAt: string | null;
      callEndedAt: string | null;
      assignedDoctorID: string | null;
      consultationId: string | null;
      fullName: string;
    }

    let rows: PatientQueueRow[] = [];

    let consultationId = null;
    if (consultationResult.rows.length > 0) {
      consultationId = consultationResult.rows[0].id;
    }

    // Get the current assigned doctor ID, if any
    const existingAssignmentQuery = `
      SELECT "assignedDoctorID" FROM PatientQueue WHERE "patientID" = $1
    `;
    const existingAssignment = await client.query(existingAssignmentQuery, [patientID]);
    const currentAssignedDoctorID =
      existingAssignment.rows.length > 0 ? existingAssignment.rows[0].assignedDoctorID : null;

    // Find the doctor this patient is actually booked with
    const doctorResult = await client.query(findDoctorQuery, [patientID]);
    const bookedDoctorID = doctorResult.rows.length > 0 ? doctorResult.rows[0].doctor_access_id : null;

    // Determine which doctor ID to use - always prefer the booked doctor if available
    let assignedDoctorID = bookedDoctorID;

    if (bookedDoctorID) {
      if (!currentAssignedDoctorID || bookedDoctorID !== currentAssignedDoctorID) {
        logger.info(
          `Found assigned doctor ${bookedDoctorID} for patient ${patientID} (was ${currentAssignedDoctorID})`,
        );
      } else {
        logger.info(`Confirmed doctor ${bookedDoctorID} is correctly assigned to patient ${patientID}`);
      }
    } else {
      // If no booked doctor found, keep the existing assignment if any
      assignedDoctorID = currentAssignedDoctorID;
      if (currentAssignedDoctorID) {
        logger.info(
          `No booked doctor found for patient ${patientID}, keeping current assignment: ${currentAssignedDoctorID}`,
        );
      } else {
        logger.warn(`No booked doctor or current assignment found for patient ${patientID}`);
      }
    }

    if (consultationId) {
      // If we have a consultation ID, use it for the specific record
      logger.info(`Found consultation ${consultationId} for patient ${patientID}`);
      const result = await client.query(query, [
        patientID,
        'ONLINE',
        patient?.email,
        true,
        assignedDoctorID,
        consultationId,
      ]);
      rows = result.rows;

      // Update the consultation tag
      await client.query(updateConsultationTag, [consultationId]);
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID}, using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'ONLINE', patient?.email, true, assignedDoctorID]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'ONLINE', patient?.email]);

    // If we found an assigned doctor, ensure proper assignment in both tables
    if (assignedDoctorID) {
      await assignPatientToDoctor(db, patientID, assignedDoctorID, client);
    }

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status ONLINE`);
    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const postPatientOffline: RequestHandler = catchAll(async (req, res) => {
  // they should already existing in queue table,
  // status to ONLINE,
  const client = await db.connect();
  const patientID = req.body.patientID;

  const consultation = `
  WITH LatestConsultation AS (
    SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
    FROM Consultation c
    WHERE c."patientID" = $1
    ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation per patient
  )
  SELECT lc.id, lc."patientID"
  FROM LatestConsultation lc
  WHERE
      lc."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND lc."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND lc.completed = FALSE
      AND lc."notificationSent" = TRUE
  LIMIT 1;`;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "leftAt", "status", email, "consultationId", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, $4, $5)
    ON CONFLICT ("patientID", "consultationId") 
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "leftAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
    WHERE 
      PatientQueue."patientID" = $1 AND PatientQueue."consultationId" = $4
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
)
SELECT
    u.*,
    p."fullName"
FROM upserted u
JOIN Patient p ON u."patientID" = p."patientID";
`;

  // Fallback query for when no consultation ID is found
  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "leftAt", "status", email)
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "leftAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
)
SELECT
    u.*,
    p."fullName"
FROM upserted u
JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
      WITH upserted AS (
        INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "leftAt", "status", email)
        VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3)
        RETURNING
          "patientID",
          "status",
          ("createdAt" ${timeZone}) AS "createdAt",
          ("updatedAt" ${timeZone}) AS "updatedAt",
          ("joinedAt" ${timeZone}) AS "joinedAt",
          "notificationSent",
          ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
          ("leftAt" ${timeZone}) AS "leftAt",
          ("completedAt" ${timeZone}) AS "completedAt",
          ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
          ("admittedAt" ${timeZone}) AS "admittedAt",
          ("confirmedAt" ${timeZone}) AS "confirmedAt",
          ("callEndedAt" ${timeZone}) AS "callEndedAt"
      )
      SELECT
        u.*,
        p."fullName"
      FROM upserted u
      JOIN Patient p ON u."patientID" = p."patientID";
      `;

  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Get the PatientQueue record with today's consultation
    const { consultationId, assignedDoctorID } = await getPatientQueueWithTodayConsultation(patientID, client);

    // If there's no consultation ID in the existing record, try to find one for today
    let finalConsultationId = consultationId;
    if (!finalConsultationId) {
      const consultationResult = await client.query(consultation, [patientID]);
      finalConsultationId = consultationResult.rows.length > 0 ? consultationResult.rows[0].id : null;

      if (finalConsultationId) {
        logger.info(`Found consultation ${finalConsultationId} for patient ${patientID}`);
      }
    }

    let rows;

    if (finalConsultationId) {
      // If we have a consultation ID, use it to update the specific record
      const result = await client.query(query, [
        patientID,
        'OFFLINE',
        patient?.email,
        finalConsultationId,
        assignedDoctorID,
      ]);
      rows = result.rows;
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID}, using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'OFFLINE', patient?.email]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'OFFLINE', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status OFFLINE`);
    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const leftPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  let patientID = '';
  if (req.method === 'PUT') {
    patientID = req.params.id;
  } else if (req.method === 'POST') {
    const body = JSON.parse(req.body);
    patientID = body.token;
  }

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "leftAt", "consultationId", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP, $4, $5)
    ON CONFLICT ("patientID", "consultationId") 
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "leftAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
    WHERE 
      PatientQueue."patientID" = $1 AND PatientQueue."consultationId" = $4
RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  // Fallback query for when no consultation ID is found
  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "leftAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "leftAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
WITH upserted AS (
  INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "leftAt")
  VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
RETURNING
    "patientID",
    "status",
    ("createdAt" ${timeZone}) AS "createdAt",
    ("updatedAt" ${timeZone}) AS "updatedAt",
    ("joinedAt" ${timeZone}) AS "joinedAt",
    "notificationSent",
    ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
    ("leftAt" ${timeZone}) AS "leftAt",
    ("completedAt" ${timeZone}) AS "completedAt",
    ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
    ("admittedAt" ${timeZone}) AS "admittedAt",
    ("confirmedAt" ${timeZone}) AS "confirmedAt",
    ("callEndedAt" ${timeZone}) AS "callEndedAt"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
    `;

  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Get the PatientQueue record with today's consultation
    const { consultationId, assignedDoctorID } = await getPatientQueueWithTodayConsultation(patientID, client);

    let rows;

    if (consultationId) {
      // If we have a consultation ID, use it to update the specific record
      logger.info(`Found consultation ${consultationId} for patient ${patientID} - updating specific record`);
      const result = await client.query(query, [patientID, 'AWAY', patient?.email, consultationId, assignedDoctorID]);
      rows = result.rows;
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID} - using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'AWAY', patient?.email]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'AWAY', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status AWAY`);
    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const joinedCallPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  const patientID = req.params.id;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "joinedCallAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "joinedCallAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID"
    )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;
  const patientQueueDetails = `
  WITH upserted AS (
    INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "joinedCallAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
  RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt"
    )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
  `;

  try {
    // Check if this is Zelda test patient - prevent her from joining calls
    if (config.zelda?.enabled && config.zelda?.patientEmail) {
      try {
        const patientCheck = await client.query(`SELECT email FROM Patient WHERE "patientID" = $1`, [patientID]);

        if (patientCheck.rows.length > 0 && patientCheck.rows[0].email === config.zelda.patientEmail) {
          logger.info(`Zelda test patient (${config.zelda.patientEmail}) attempted to join call - blocked`);
          res.status(200).send({ message: 'Test patient cannot join calls' });
          return;
        }
      } catch (error) {
        logger.error('Error checking if patient is Zelda:', error);
        // Continue with normal flow if check fails
      }
    }

    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Check if there's an existing assigned doctor for this patient
    const existingAssignmentQuery = `
      SELECT "assignedDoctorID" FROM PatientQueue WHERE "patientID" = $1
    `;
    const existingAssignment = await client.query(existingAssignmentQuery, [patientID]);
    const assignedDoctorID = existingAssignment.rows.length > 0 ? existingAssignment.rows[0].assignedDoctorID : null;

    if (assignedDoctorID) {
      logger.info(
        `Preserving doctor assignment (${assignedDoctorID}) for patient ${patientID} while setting status to JOINED`,
      );
    }

    const { rows } = await client.query(query, [patientID, 'JOINED', patient?.email]);
    await client.query(patientQueueDetails, [patientID, 'JOINED', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status JOINED`);

    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const completedPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  const patientID = req.params.id;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "completedAt", "consultationId", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP, $4, $5)
    ON CONFLICT ("patientID", "consultationId") 
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "completedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
    WHERE 
      PatientQueue."patientID" = $1 AND PatientQueue."consultationId" = $4
  RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
      )
  SELECT
      u.*,
      p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  // Fallback query for when no consultation ID is found
  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "completedAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "completedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
  RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
      )
  SELECT
      u.*,
      p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
    WITH upserted AS (
      INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "completedAt")
      VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
      RETURNING
        "patientID",
        "status",
        ("createdAt" ${timeZone}) AS "createdAt",
        ("updatedAt" ${timeZone}) AS "updatedAt",
        ("joinedAt" ${timeZone}) AS "joinedAt",
        "notificationSent",
        ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
        ("leftAt" ${timeZone}) AS "leftAt",
        ("completedAt" ${timeZone}) AS "completedAt",
        ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
        ("admittedAt" ${timeZone}) AS "admittedAt",
        ("confirmedAt" ${timeZone}) AS "confirmedAt",
        ("callEndedAt" ${timeZone}) AS "callEndedAt"
      )
      SELECT
        u.*,
        p."fullName"
      FROM upserted u
      JOIN Patient p ON u."patientID" = p."patientID";
    `;

  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Get the PatientQueue record with today's consultation
    const { consultationId, assignedDoctorID } = await getPatientQueueWithTodayConsultation(patientID, client);

    let rows;

    if (consultationId) {
      // If we have a consultation ID, use it to update the specific record
      logger.info(
        `Found consultation ${consultationId} for patient ${patientID} - updating specific record and marking consultation as completed`,
      );
      const result = await client.query(query, [
        patientID,
        'COMPLETED',
        patient?.email,
        consultationId,
        assignedDoctorID,
      ]);
      rows = result.rows;

      // Mark the consultation as completed
      await client.query(`UPDATE Consultation SET completed = TRUE WHERE id = $1`, [consultationId]);
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID} - using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'COMPLETED', patient?.email]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'COMPLETED', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status COMPLETED`);

    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchLatestAdmission: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  const query = `
    WITH RankedAdmissions AS (
    SELECT
        *,
        ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
    FROM
        Admission
      )
      SELECT *
      FROM RankedAdmissions
      WHERE row_rank = 1;`;

  try {
    const { rows } = await client.query(query);
    const lastestAdmission = rows as Admission[];
    if (lastestAdmission.length > 0) {
      res.status(200).send(lastestAdmission);
      return;
    } else {
      res.status(200).send([]);
      return;
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// export const updateAdmission: RequestHandler = catchAll(async (req, res) => {
//   // TODO
//   // update latest admission to admitted = false
//   const client = await db.connect();

//   const patientID = req.params.id;
//   const status = req.body.status;
//   const query = `
//     UPDATE Admission
//     SET "admitted" = $1, "updatedAt" = CURRENT_TIMESTAMP
//     WHERE "patientID" = $2
//   `;

//   try {
//     await client.query('BEGIN');
//     const { rows } = await client.query(query, [status, patientID]);
//     await client.query('COMMIT');
//     res.status(200).send(rows[0]);
//   } catch (e) {
//     await client.query('ROLLBACK');
//     const error = e as AxiosError;
//     throw new ApiError(httpStatus.BAD_REQUEST, error.message);
//   } finally {
//     client.release();
//   }
// });

export const fetchQueue: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  // Query to fetch patients with consultations scheduled for today using consultationId for proper filtering
  // This prevents cross-day contamination by ensuring PatientQueue entries are tied to today's consultations
  const query = `
  WITH TodayConsultations AS (
    -- Get all consultations scheduled for today with doctor assignments
    SELECT DISTINCT ON (c."patientID")
      c.id,
      c."patientID",
      c."consultationDate",
      c.completed
    FROM Consultation c
    JOIN Patient p ON c."patientID" = p."patientID"
    JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
    JOIN Range r ON ps.range_id = r.id
    WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
    ORDER BY c."patientID", c."createdAt" DESC
  ),
  TotalPatientsForDay AS (
    -- Count ALL patients with consultations scheduled for today regardless of queue status
    SELECT COUNT(DISTINCT c."patientID") as total_count
    FROM Consultation c
    JOIN Patient p ON c."patientID" = p."patientID"
    JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
    JOIN Range r ON ps.range_id = r.id
    WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
      AND p."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}  -- Filter test patients
      AND (p.email IS NULL OR p.email != '${config.zelda.patientEmail}')  -- Filter out Zelda patients
  ),
  QueueWithTodayConsultations AS (
    -- Get PatientQueue entries that are linked to today's consultations via consultationId
    SELECT
        pq.id,
        pq."patientID",
        TO_CHAR((pq."createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        TO_CHAR((pq."updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
        TO_CHAR((pq."joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
        pq."notificationSent",
        TO_CHAR((pq."notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
        TO_CHAR((pq."leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
        TO_CHAR((pq."joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
        TO_CHAR((pq."admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
        TO_CHAR((pq."completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
        TO_CHAR((pq."callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
        TO_CHAR((pq."confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
        TO_CHAR((pq."firstTimeJoined" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "firstTimeJoined",
        pq.status,
        pq."assignedDoctorID",
        pq."consultedDoctorID",
        pq."noShow",
        pq."tech_issue",
        pq.attempt,
        pq.allowed,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId",
        tc.completed AS "consultationCompleted",
        pq."consultationId" AS "linkedConsultationId"
    FROM PatientQueue pq
    JOIN Patient p ON pq."patientID" = p."patientID"
    JOIN TodayConsultations tc ON pq."consultationId" = tc.id  -- Use consultationId for exact matching
  ),
  QueueWithoutConsultationId AS (
    -- Handle PatientQueue entries that don't have consultationId but should be included for today
    -- These are filtered by date to ensure they're from today's activity
    SELECT
        pq.id,
        pq."patientID",
        TO_CHAR((pq."createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        TO_CHAR((pq."updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
        TO_CHAR((pq."joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
        pq."notificationSent",
        TO_CHAR((pq."notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
        TO_CHAR((pq."leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
        TO_CHAR((pq."joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
        TO_CHAR((pq."admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
        TO_CHAR((pq."completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
        TO_CHAR((pq."callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
        TO_CHAR((pq."confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
        TO_CHAR((pq."firstTimeJoined" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "firstTimeJoined",
        pq.status,
        pq."assignedDoctorID",
        pq."consultedDoctorID",
        pq."noShow",
        pq."tech_issue",
        pq.attempt,
        pq.allowed,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId",
        tc.completed AS "consultationCompleted",
        pq."consultationId" AS "linkedConsultationId"
    FROM PatientQueue pq
    JOIN Patient p ON pq."patientID" = p."patientID"
    LEFT JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"  -- Match by patientID for fallback
    WHERE pq."consultationId" IS NULL  -- Only entries without consultationId
      AND tc.id IS NOT NULL  -- Must have a consultation today
      AND (
        -- Filter by today's queue activity
        pq."createdAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        OR pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
        OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
      )
  )
  -- Combine both result sets
  SELECT * FROM QueueWithTodayConsultations
  UNION ALL
  SELECT * FROM QueueWithoutConsultationId
  ORDER BY "consultationDate" ASC, "createdAt" DESC;
`;

  // Separate query to get total patients count for the day
  const totalCountQuery = `
    SELECT COUNT(DISTINCT c."patientID") as total_count
    FROM Consultation c
    JOIN Patient p ON c."patientID" = p."patientID"
    JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
    JOIN Range r ON ps.range_id = r.id
    WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
      AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      AND r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
      AND p."fullName" ${config.devMode ? `ILIKE '%TESTX%'` : `NOT ILIKE '%TESTX%'`}  -- Filter test patients
      AND (p.email IS NULL OR p.email != '${config.zelda.patientEmail}')  -- Filter out Zelda patients
  `;

  try {
    await client.query('BEGIN');
    const [queueResult, totalResult] = await Promise.all([client.query(query), client.query(totalCountQuery)]);
    await client.query('COMMIT');

    const totalPatientsForDay = parseInt(totalResult.rows[0]?.total_count || '0', 10);

    res.status(200).send({
      patients: queueResult.rows,
      totalPatientsForDay,
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchQueueDetailsById: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const client = await db.connect();
  const query = `
    WITH LatestConsultation AS (
      SELECT DISTINCT ON (c."patientID")
        c."patientID",
        c."consultationDate",
        c.id
      FROM Consultation c
      WHERE c."patientID" = $1
        AND c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
        AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
      ORDER BY c."patientID", c."createdAt" DESC
    )
    SELECT 
      pq."patientID",
      pq."status",
      TO_CHAR((pq."createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
      TO_CHAR((pq."updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
      TO_CHAR((pq."joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
      TO_CHAR((pq."notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
      TO_CHAR((pq."leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
      TO_CHAR((pq."completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
      TO_CHAR((pq."joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
      TO_CHAR((pq."callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
      TO_CHAR((pq."confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
      TO_CHAR((pq."admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
      pq."notificationSent",
      TO_CHAR((lc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
      lc.id AS "consultationId"
    FROM PatientQueueDetails pq
    LEFT JOIN LatestConsultation lc ON pq."patientID" = lc."patientID"
    WHERE pq."patientID" = $1 
    ORDER BY pq."createdAt" ASC;`;

  try {
    const { rows } = await client.query(query, [patientID]);
    res.status(200).send(rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const emptyQueue: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  const patientQueue = `DELETE FROM PatientQueue;`;
  const patientQueueDetails = `DELETE FROM PatientQueueDetails;`;
  const patientAdmission = `DELETE FROM Admission;`;

  try {
    await client.query('BEGIN');
    await client.query(patientQueue);
    await client.query(patientQueueDetails);
    await client.query(patientAdmission);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.EMPTY_QUEUE, []);
    res.status(200).send([]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchOnlineQueue: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  const query = `
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
        SELECT DISTINCT ON (c."patientID")
          c.id,
          c.id,
          c."patientID",
          c."consultationDate"
          c."consultationDate"
        FROM Consultation c
        WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
          AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
        ORDER BY c."patientID", c."createdAt" DESC
      )
      SELECT
        pq.*,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
      WHERE pq.status = 'ONLINE'
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
      ORDER BY
        pq."createdAt" DESC;
  `;

  try {
    await client.query('BEGIN');
    const { rows } = await client.query(query);

    await client.query('COMMIT');
    res.status(200).send(rows);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchJoinedQueue: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  const query = `
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
        SELECT DISTINCT ON (c."patientID")
          c.id,
          c.id,
          c."patientID",
          c."consultationDate"
          c."consultationDate"
        FROM Consultation c
        WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
          AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
        ORDER BY c."patientID", c."createdAt" DESC
      )
      SELECT
        pq.*,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
      WHERE pq.status = 'JOINED'
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
      ORDER BY
        pq."createdAt" DESC;
  `;

  try {
    await client.query('BEGIN');
    const { rows } = await client.query(query);

    await client.query('COMMIT');
    res.status(200).send(rows);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getPatientById: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const patient = patients.find((p) => p.patientID === patientID);
  if (patient) {
    res.status(200).send(patient);
  } else {
    res.status(200).send({});
  }
});

/**
 * Parses a date string in any common format and returns a valid DateTime object
 * Handles both date and time components, and converts from Australia/Sydney timezone to UTC
 * @param dateString The date string to parse
 * @returns A valid DateTime object or an invalid DateTime with error info
 */
const parseAnyDate = (dateString: string): DateTime => {
  if (!dateString) {
    return DateTime.invalid('Empty date string');
  }

  // Check if the string contains both date and time
  const hasTime = dateString.includes(':') || dateString.includes('T');

  if (hasTime) {
    // Try parsing as ISO format with timezone conversion
    let parsed: DateTime;

    // Try ISO format first (YYYY-MM-DDTHH:mm:ss)
    parsed = DateTime.fromISO(dateString, { zone: 'Australia/Sydney' });
    if (parsed.isValid) {
      return parsed;
    }

    // Try common datetime formats with space separator
    const dateTimeFormats = [
      'yyyy-MM-dd HH:mm:ss',
      'yyyy-MM-dd HH:mm',
      'MM-dd-yyyy HH:mm:ss',
      'MM-dd-yyyy HH:mm',
      'dd-MM-yyyy HH:mm:ss',
      'dd-MM-yyyy HH:mm',
      'yyyy/MM/dd HH:mm:ss',
      'yyyy/MM/dd HH:mm',
      'MM/dd/yyyy HH:mm:ss',
      'MM/dd/yyyy HH:mm',
      'dd/MM/yyyy HH:mm:ss', // Added format for DD/MM/YYYY HH:mm:ss
      'dd/MM/yyyy HH:mm', // Added format for DD/MM/YYYY HH:mm
    ];

    for (const format of dateTimeFormats) {
      parsed = DateTime.fromFormat(dateString, format, { zone: 'Australia/Sydney' });
      if (parsed.isValid) {
        return parsed;
      }
    }
  }

  // If no time component or datetime parsing failed, try date-only formats
  const datePart = dateString.split(' ')[0].split('T')[0]; // Extract date part

  // Try common date formats
  const dateFormats = ['yyyy-MM-dd', 'MM-dd-yyyy', 'dd-MM-yyyy', 'yyyy/MM/dd', 'MM/dd/yyyy', 'dd/MM/yyyy'];

  for (const format of dateFormats) {
    const parsed = DateTime.fromFormat(datePart, format, { zone: 'Australia/Sydney' });
    if (parsed.isValid) {
      return parsed;
    }
  }

  // If all attempts fail, try ISO parsing of just the date part as a last resort
  const isoAttempt = DateTime.fromISO(datePart, { zone: 'Australia/Sydney' });
  if (isoAttempt.isValid) {
    return isoAttempt;
  }

  // Return invalid DateTime with original string for debugging
  return DateTime.invalid('Unparseable date: ' + dateString);
};
export const getPatientByPatientId: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const client = await db.connect();
  try {
    const query = `SELECT * FROM Patient WHERE "patientID" = $1`;
    const result = await client.query(query, [patientID]);
    res.status(200).send(result.rows[0]);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getPatientByIdFromDB: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const client = await db.connect();

  try {
    const query = `SELECT * FROM Patient WHERE "patientID" = $1`;
    const result = await client.query(query, [patientID]);
    res.status(200).send(result.rows[0]);
  } catch (error) {
    console.error('Error fetching patient by ID:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

export const getPatientByZohoId: RequestHandler = catchAll(async (req, res) => {
  const zohoId = req.params.id;
  const client = await db.connect();

  try {
    const query = `SELECT * FROM Patient WHERE "zohoID" = $1`;
    const result = await client.query(query, [zohoId]);

    if (result.rows.length > 0) {
      res.status(200).send(result.rows[0]);
    } else {
      res.status(404).send({ error: 'Patient not found' });
    }
  } catch (error) {
    console.error('Error fetching patient by zohoId:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

export const getPatientByEmail: RequestHandler = catchAll(async (req, res) => {
  const email = req.params.email;

  if (!email) {
    res.status(400).json({
      success: false,
      message: 'Email parameter is required',
      data: null
    });
    return;
  }

  const client = await db.connect();

  try {
    // Decode the email if it's URL encoded
    const decodedEmail = decodeURIComponent(email);

    // Clean up the email - remove any query parameters that might be attached
    const cleanEmail = decodedEmail.split('?')[0];

    
    const query = `
      SELECT
        id,
        "fullName",
        email,
        "mobile",
        "patientID",
        "zohoID",
        "returningPatient",
        "dob",
        state,
        "createdAt",
        "updatedAt"
      FROM Patient
      WHERE email = $1
    `;

    const result = await client.query(query, [cleanEmail.toLowerCase()]);

    if (result.rows.length > 0) {
      res.status(200).json({
        success: true,
        data: result.rows[0]
      });
    } else {
      res.status(404).json({
        success: false,
        message: 'Patient not found',
        data: null
      });
    }
  } catch (error) {
    logger.error('Error fetching patient by email:', error);
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      data: null
    });
  } finally {
    client.release();
  }
});

// Delete patient slot when remove in CRM
export const completePatientConsultation: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.body.patientID;
  const leadID = req.body.leadID;

  // Parse date with robust utility function
  const dt = parseAnyDate(req.body.date);

  // Validate before proceeding
  if (!dt.isValid) {
    logger.error(`Could not parse date: ${req.body.date}. Error: ${dt.invalidReason}`);
    res.status(400).send({ error: 'Invalid date format' });
    return;
  }

  // Always format to your DB expected format
  const formattedDate = dt.toFormat('yyyy-MM-dd');

  const query = `
  UPDATE Consultation
    SET
        "completed" = true,
        "updatedAt" = CURRENT_TIMESTAMP
    WHERE id = (
        SELECT id
        FROM Consultation
        WHERE "patientID" = $1
        ORDER BY "createdAt" DESC
        LIMIT 1
    );`;

  // Regular slot increment query
  const increamentRegularSlot = `
      UPDATE Slot
      SET remaining = remaining + 1, "updatedAt"=CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *;
      `;

  // NoShow slot increment query
  const increamentNoShowSlot = `
      UPDATE Slot
      SET "noShowRemaining" = "noShowRemaining" + 1, "updatedAt"=CURRENT_TIMESTAMP
      WHERE id = $1
      RETURNING *;
      `;

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Mark the consultation as completed
    await client.query(query, [patientID]);

    // Delete the patient slot and get its queueType
    const freeCalendarQuery = `
      DELETE FROM PatientSlot ps
      USING Range r
      WHERE ps.range_id = r.id
      AND ps.patient_id = $1
      AND r.date = $2
      RETURNING ps.*, ps."queueType";`;

    const deletedSlot = await client.query(freeCalendarQuery, [leadID, formattedDate]);

    if (deletedSlot.rows.length > 0) {
      // Get the queueType from the deleted slot
      const queueType = deletedSlot.rows[0].queueType || 'regular'; // Default to regular if not set

      // Choose the appropriate increment query based on queueType
      const incrementQuery = queueType === 'noShow' ? increamentNoShowSlot : increamentRegularSlot;

      // Increment the appropriate slot pool
      const updatedSlot = await client.query(incrementQuery, [deletedSlot.rows[0].slot_id]);

      logger.info(
        `Consultation for patient ${patientID} has been removed from Calendar for starting date ${formattedDate}. ` +
          `Incremented ${queueType} pool.`,
      );
      WebSocketManager.dispatch(TOPICS.NEW_BOOKING, [updatedSlot.rows[0]]);
    } else {
      // If no slot was found, check if date exists in Range table
      const checkDateQuery = `SELECT COUNT(*) FROM Range WHERE date = $1`;
      const dateExists = await client.query(checkDateQuery, [formattedDate]);

      if (dateExists.rows[0].count === '0') {
        logger.warn(`No bookings found for date ${formattedDate} in Range table`);
      } else {
        logger.info(`Patient ${patientID} has no booking for starting date ${formattedDate}`);
      }
    }
    await client.query('COMMIT');
    res.status(200).send({});
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error(`Error in completePatientConsultation: ${error}`);
    console.error('Error inserting/updating patient:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

export const getPatientsFullHistory: RequestHandler = catchAll(async (req, res) => {
  const searchParams = req.body.searchParams;
  const client = await db.connect();

  try {
    const result = await client.query(Queries.patientHistory(), [searchParams]);
    res.status(200).send(result.rows);
  } catch (error) {
    console.error('Error getting patients histories:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

export const getSearchedPatients: RequestHandler = catchAll(async (req, res) => {
  const searchParams = req.body.searchParams;
  const client = await db.connect();
  const query = `SELECT * FROM Patient p WHERE p."fullName" ILIKE '%' || $1 || '%'`;
  try {
    const result = await client.query(query, [searchParams]);
    res.status(200).send(result.rows);
  } catch (error) {
    console.error('Error getting patients histories:', error);
    res.status(500).send({ error: 'Internal server error' });
  } finally {
    client.release();
  }
});

export const deleteTimer: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const uniqueKey = req.params.drId || 'patientInterval';
  const recordStartTimer = `
  INSERT INTO doctorstartendtimer ("drId", action, "createdAt", "updatedAt")
  VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
`;

  try {
    await client.query('BEGIN');
    if (timerMap.has(uniqueKey)) {
      const { timerId } = timerMap.get(uniqueKey);
      clearInterval(timerId);
      const deleted = timerMap.delete(uniqueKey);
      if (deleted) {
        // if (timerMap.size <= 0) {
        //   noShowMap.delete('noShowTimer');
        // }

        WebSocketManager.dispatch(TOPICS.CLEAR_TIMER, {
          timerKey: uniqueKey,
        });
        logger.info(`Timer ${uniqueKey} has been deleted`);
        notificationNumber = undefined;

        if (uniqueKey) {
          await client.query(recordStartTimer, [uniqueKey, 'delete']);

          // Integrate Zelda management when doctor timer ends
          try {
            const zeldaManager = ZeldaManager.getInstance(db);
            await zeldaManager.manageZelda(uniqueKey, 'TIMER_END');
          } catch (zeldaError) {
            logger.error(`Error in Zelda management for doctor ${uniqueKey}:`, zeldaError);
            // Don't fail the timer deletion if Zelda management fails
          }

          await client.query('COMMIT');
        }
        res.status(200).send({
          joinedPatient: [],
          onlinePatient: [],
          timerKey: uniqueKey,
        });
        return;
      }
    }
    res.status(200).send([]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchTimer: RequestHandler = catchAll(async (req, res) => {
  const uniqueKey = req.params.drId || 'patientInterval';
  if (timerMap.has(uniqueKey)) {
    res.status(200).send({ timerKey: uniqueKey });
    return;
  }
  res.status(200).send({});
});

export const fetchTimerAdmin: RequestHandler = catchAll(async (_req, res) => {
  const allKeysObject = Object.fromEntries(Array.from(timerMap.keys()).map((key) => [key, key]));
  res.status(200).send(allKeysObject);
});


export const startTimer: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const uniqueKey = req.body.drId || 'patientInterval';
  const timerEntry = timerMap.get(uniqueKey);

  const recordStartTimer = `
    INSERT INTO doctorstartendtimer ("drId", action, "createdAt", "updatedAt")
    VALUES ($1, $2, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `;
  const consultationTotal = `
  WITH LatestConsultation AS (
    SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
    FROM Consultation c
    JOIN Patient p ON c."patientID" = p."patientID"
    JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
    JOIN Range r ON ps.range_id = r.id
    WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
    ORDER BY c."createdAt",c."consultationDate",c."patientID" DESC  -- Get the most recent consultation per patient
)
SELECT lc.id, lc."patientID"
FROM LatestConsultation lc
WHERE
    lc."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
    AND lc."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
    AND lc.completed = FALSE`;

  const OnlineQueue = `
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
        SELECT DISTINCT ON (c."patientID")
          c.id,
          c."patientID",
          c."consultationDate"
        FROM Consultation c
        WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
          AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
          AND c.completed = false
        ORDER BY c."patientID", c."createdAt" DESC
      )
      SELECT
        pq.*,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
      WHERE pq.status = 'ONLINE'
        AND pq."joinedCallAt" IS NULL
        AND pq."notificationSent" = true
        AND pq."assignedDoctorID" = $1
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
      ORDER BY
        pq."createdAt" DESC;
  `;

  const JoinedQueue = `
      WITH TodayConsultations AS (
        -- Get all consultations scheduled for today
        SELECT DISTINCT ON (c."patientID")
          c.id,
          c."patientID",
          c."consultationDate"
        FROM Consultation c
        WHERE c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
          AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
          AND c.completed = false
        ORDER BY c."patientID", c."createdAt" DESC
      )
      SELECT
        pq.*,
        p."fullName",
        TO_CHAR((tc."consultationDate" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "consultationDate",
        tc.id AS "consultationId"
      FROM PatientQueue pq
      JOIN Patient p ON pq."patientID" = p."patientID"
      JOIN TodayConsultations tc ON pq."patientID" = tc."patientID"
      WHERE pq.status = 'JOINED'
        AND pq."assignedDoctorID" = $1
        AND (
          -- Additional safety check: ensure PatientQueue activity is from today
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          OR pq."updatedAt" ${timeZone} >= (NOW() ${timeZone})::DATE
        )
      ORDER BY
        pq."createdAt" DESC;
  `;
  try {
    await client.query('BEGIN');

    // Use the request-time client for initial queries
    const fetchOnlinePatient = await client.query(OnlineQueue, [uniqueKey]);
    const fetchJoinedPatient = await client.query(JoinedQueue, [uniqueKey]);
    const consultsTotal = await client.query(consultationTotal);
    if (uniqueKey) {
      await client.query(recordStartTimer, [uniqueKey, 'start']);
    }

    // For 1-30 consults → 1 notification
    // For 31 - 39 consults → 2 notifications
    // For 40 - 60 consults → 3 notifications
    // start adding 1 from 60 up

    if (!notificationNumber) {
      const numberOfConsults = consultsTotal.rows.length;

      if (numberOfConsults <= 30) {
        notificationNumber = 1;
      } else if (numberOfConsults > 30 && numberOfConsults < 40) {
        notificationNumber = 2;
      } else {
        notificationNumber = 1 + Math.floor(numberOfConsults / 20);
      }
      logger.info(`Total Consultation: ${numberOfConsults} \n Notification Number: ${notificationNumber}`);
    }

    // TODO: Update to Dr accessID
    // Each Dr should have their own timer.

    if (!timerEntry) {
      logger.info('Timer started/restarted');
      WebSocketManager.dispatch(TOPICS.UPDATE_TIMER, {
        timerKey: uniqueKey,
      });

      const timerId = setInterval(async () => {
        let intervalClient: import('pg').PoolClient | undefined;
        try {
          intervalClient = await db.connect();
          const fetchOnlinePatient = await intervalClient.query(OnlineQueue, [uniqueKey]);
          const fetchJoinedPatient = await intervalClient.query(JoinedQueue, [uniqueKey]);

          logger.info(`Joined Patient :: ${fetchJoinedPatient.rows.length}`);
          logger.info(`Online Patient ::  ${fetchOnlinePatient.rows.length}`);

          if (fetchOnlinePatient.rows.length <= 0) {
            // Use the doctor's ID for notifications
            const notifiedPatients = await notifyNextPatientMiddleWare(uniqueKey);

            // Only call warnPatient if patients were actually notified
            if (notifiedPatients && notifiedPatients.length > 0) {
              //logger.info(`${notifiedPatients.length} patients were notified, proceeding with warning system`);
              await warnPatient();
            } else {
              //logger.info(`No patients were notified, skipping warning system`);
            }
          }

          await callNoShowPatient();

          //startPatientReassignmentProcess();

        } catch (err) {
          console.error('Error during interval execution:', err);
        } finally {
          if (intervalClient) intervalClient.release();
        }
      }, 60000);

      // Add the timer to the timerMap right away so it's recognized by calculateDynamicPatientLimit
      timerMap.set(uniqueKey, { timerId });

    
      // Add a small random delay before sending initial notifications
      // This helps prevent race conditions when multiple doctors start their shifts at the same time
      const randomDelay = Math.floor(Math.random() * 1000) + 500; // Random delay between 500-1500ms

      setTimeout(async () => {
        try {
          await notifyNextPatientMiddleWare(uniqueKey);
          await warnPatient();
        } catch (error) {
          logger.error(`Error sending initial notifications for doctor ${uniqueKey}:`, error);
        }
      }, randomDelay);
    }

    // Run every 2 min to check for now shows: No movement for last 10min since Notification was sent.
    // const noShowTimer = setInterval(async () => {
    //   await callNoShowPatient();
    //   startPatientReassignmentProcess();
    // }, 60000);

    //noShowMap.set('noShowTimer', { noShowTimer });

    res.status(200).send({
      joinedPatient: fetchJoinedPatient.rows,
      onlinePatient: fetchOnlinePatient.rows,
      timerKey: uniqueKey,
    });
    await client.query('COMMIT');
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchAllInbox: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const page = req.query.page || 1;
  const limit = 60;
  const offset = (Number(page) - 1) * Number(limit);
  try {
    const result = await client.query(Queries.InboxData(), [limit, offset]);
    res.status(200).send(result.rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchInboxByEmail: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const email = req.params.email;

  const page = req.query.page || 1;
  const limit = 60;
  const offset = (Number(page) - 1) * Number(limit);

  try {
    const result = await client.query(Queries.InboxDataByEmail(), [email, limit, offset]);
    res.status(200).send(result.rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchConsultationByDate: RequestHandler = catchAll(async (req, res) => {
  const date = req.body.date as string;
  const client = await db.connect();
  const query = `SELECT
    c.id,
    c."patientID",
    c."drId",
    c.email,
    c."joinedAt",
    c."consultationDate" ${timeZone} AS "consultationDate",
    c."meetingOngoing",
    c."drJoined",
    c."consultationStart" ${timeZone} AS "consultationStart",
    c."consultationEnd" ${timeZone} AS "consultationEnd",
    c."notificationSent",
    c."notificationSentDateTime" ${timeZone} AS "notificationSentDateTime",
    c."completed",
    c."createdAt" ${timeZone} AS "createdAt",
    c."updatedAt" ${timeZone} AS "updatedAt",
    p."fullName",
    p."password",
    p."returningPatient",
    p."zohoID",
    p.locked,
    p."drLocked",
    p.state,
    p."lastCompletedForm"
    FROM consultation c
    LEFT JOIN patient p ON c.email = p.email
    LEFT JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
    LEFT JOIN Range r ON ps.range_id = r.id
    WHERE DATE(c."consultationDate") = $1
    AND (r."doctorID" IS NOT NULL OR r."doctorID" IS NULL);`;

  try {
    const result = await client.query(query, [date]);
    res.status(200).send(result.rows);
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchTreatmentPlanById: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.params.id;
  const query = `SELECT "patientID" FROM treatmentplan WHERE "patientID" = $1`;

  const consultation = `
  WITH LatestConsultation AS (
        -- ✅ Step 1: Select the latest consultation for this patient (regardless of date)
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."patientID" = $1
        ORDER BY c."createdAt",c."consultationDate", c."patientID" DESC  -- Get the most recent consultation per patient
    )
    SELECT lc.id, lc."patientID"
    FROM LatestConsultation lc
    WHERE
        lc."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
        AND lc."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
        AND lc.completed = FALSE
    LIMIT 1;`;

  try {
    const treatmentPlanResult = await client.query(query, [patientID]);
    const consultationResult = await client.query(consultation, [patientID]);

    if (consultationResult.rows.length > 0 && treatmentPlanResult.rows.length < 1) {
      res.status(200).send(consultationResult.rows);
      return;
    } else {
      res.status(200).send([]);
      return;
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getPatientDoctorInfo: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.params.id;

  const query = `
    SELECT
      d."consultationDurationMinutes",
      d.username as "doctorName",
      d."accessID" as "doctorAccessID"
    FROM Patient p
    JOIN patientslot ps ON p."zohoID" = ps.patient_id
    JOIN range r ON ps.range_id = r.id
    JOIN dr d ON r."doctorID" = d.id
    WHERE p."patientID" = $1
    AND r.date::DATE >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
    AND r.date::DATE < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
    LIMIT 1
  `;

  try {
    const result = await client.query(query, [patientID]);
    if (result.rows.length > 0) {
      res.status(200).send(result.rows[0]);
    } else {
      // Return default values if no doctor assignment found
      res.status(200).send({
        consultationDurationMinutes: 6,
        doctorName: null,
        doctorAccessID: null,
      });
    }
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const alertAwayPatientOnAdmit: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patient = req.body.patient as PatientData;
  const headers = await ZohoAuth.getHeaders();
  const query = `SELECT * FROM Patientqueue JOIN patient on patient."patientID" = Patientqueue."patientID"  WHERE Patientqueue."patientID"=$1`;
  const patientID = patient.patientID;

  const data = {
    data: [
      {
        Sales_Person: 'yes',
      },
    ],
  };

  try {
    const result = await client.query(query, [patientID]);
    if (result.rows?.length > 0 && result.rows?.[0].status === 'AWAY') {
      await axios.put(`${zohoLeadURL}/${result.rows?.[0].zohoID}`, data, { headers });
      logger.info(`Patient ${result.rows?.[0].fullName} has been called back online.`);
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESSFULLY NOTIFIED PATIENT', { id: patientID }, true));
      return;
    }
    res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT WAS NOT NOTIFIED', { id: patientID }, true));
  } catch (e) {
    const error = e as AxiosError;
    logger.error(`Error alerting patient ${patient.patientID} (alertAwayPatientOnAdmit)`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getNextPatientAutomatically: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const drId = req.params.drId as string;
  // can be selected by any Doctor

  // Simplified single query that handles all patients with attempt <= 0
  // Filters by doctor assignment through patientslot and range tables
  // Only focuses on ONLINE or JOINED patients

  const nextPatientQuery = `
   WITH RankedAdmissions AS (
    SELECT
        "email",
        "patientID",
        "drId",
        ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
    FROM
        Admission
      ),
      LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      )
    SELECT pq.*
    FROM patientqueue pq
    JOIN patient p ON pq."patientID" = p."patientID"  -- ✅ Join by patientID instead of email
    JOIN patientslot ps ON p."zohoID" = ps.patient_id  -- ✅ Join with patientslot using zohoID
    JOIN range r ON ps.range_id = r.id  -- ✅ Join with range table
    JOIN dr d ON r."doctorID" = d.id  -- ✅ Join with doctor table using range.doctorID
    LEFT JOIN RankedAdmissions ra ON pq."patientID" = ra."patientID" AND ra.row_rank = 1  -- Exclude last admitted patient
    LEFT JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
    WHERE
        pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND pq."completedAt" IS NULL
        AND (pq.status = 'ONLINE' OR pq.status = 'JOINED')  -- ✅ Only ONLINE or JOINED patients
        AND (pq.attempt IS NULL OR pq.attempt <= 0)  -- ✅ Only patients with no attempts or attempt <= 0
        AND d."accessID" = $1  -- ✅ Filter by doctor's accessID
        AND (ra."patientID" IS NULL OR ra."drId" = $1)  -- ✅ Ensures the patient is not the last admitted patient
        AND (lc.completed = false OR lc.completed IS NULL)  -- ✅ Only include patients with incomplete consultations
    ORDER BY pq."joinedCallAt" ASC, p."returningPatient" DESC, p."riskRating" ASC, pq."updatedAt" ASC
    LIMIT 1;`;

  try {
    // Removed activeAdmissionCheck as it was blocking legitimate patient selection
    // due to lack of date filtering and could prevent doctors from getting new patients
    // when old admissions from previous days exist. The RankedAdmissions CTE in the
    // main queries already prevents selecting the same patient that was just admitted.

    // Fetch next available patient (simplified single query approach)
    // Removed activeAdmissionCheck as it was blocking legitimate patient selection
    // due to lack of date filtering and could prevent doctors from getting new patients
    // when old admissions from previous days exist. The RankedAdmissions CTE in the
    // main queries already prevents selecting the same patient that was just admitted.

    // Fetch next available patient (simplified single query approach)
    await client.query('BEGIN');

    const result = await client.query(nextPatientQuery, [drId]);
    if (result.rows?.length > 0) {
      await client.query('COMMIT');
      logger.info(
        `Admitted next patient Auto: ${result.rows?.[0].patientID}, attempt: ${result.rows?.[0].attempt}`,
        `Admitted next patient Auto: ${result.rows?.[0].patientID}, attempt: ${result.rows?.[0].attempt}`,
      );
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows[0], true));
      return;
    } else {
      await client.query('COMMIT');
      logger.info('No available patient found. Redirect Back to Online List');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      await client.query('COMMIT');
      logger.info('No available patient found. Redirect Back to Online List');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
    }
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

/**
 * Updates a patient to no-show status and finds the next patient for the doctor.
 *
 * IMPORTANT: This method now follows the same approach as getNextPatientsForDoctor
 * by properly filtering patients through the PatientSlot and Range tables to ensure
 * only patients actually assigned to the requesting doctor are selected.
 *
 * Previous implementation was selecting from all online patients regardless of
 * doctor assignment, which could lead to incorrect patient selection.
 */
export const updatePatientToNoShowInQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  // Update to handle minimal data format - just need patientID
  const patientID = req.body.data.patientID as string;
  const drId = req.body.drId as string;

  // Check if the doctor already has an active admission
  const activeAdmissionCheck = `
    SELECT 1
    FROM patientqueue pq
    WHERE pq."completedAt" IS NULL
    AND pq.status = 'ADMITTED'
    AND pq."consultedDoctorID" = $1  -- Check for this specific doctor
    AND pq."patientID" != $2  -- Exclude the current patient being marked as no-show
    AND pq."updatedAt" > (CURRENT_TIMESTAMP - INTERVAL '31 seconds')  -- Only consider admissions less than 31 seconds old
    LIMIT 1;
  `;
  // Only increment attempt if patient doesn't already have noShow=true and attempt>=1
  const updatePatientAttempt = `
    UPDATE Patientqueue
    SET "noShow"=true,
        attempt = CASE
          WHEN "noShow" = true AND attempt >= 1 THEN attempt
          ELSE attempt + 1
        END,
        status = 'ENDED',
        "updatedAt"=CURRENT_TIMESTAMP,
        "consultedDoctorID"=$2
    WHERE "patientID"=$1
    RETURNING *;
  `;

  // Updated query to follow the same approach as getNextPatientsForDoctor
  // Now properly filters by doctor assignment through patientslot and range tables
  const nonNotifiedPatient = `
   WITH RankedAdmissions AS (
    SELECT
        "email",
        "patientID",
        "drId",
        ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
    FROM
        Admission
      ),
      LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      )
    SELECT pq.*
    FROM patientqueue pq
    JOIN patient p ON pq."patientID" = p."patientID"  -- ✅ Join by patientID instead of email
    JOIN patientslot ps ON p."zohoID" = ps.patient_id  -- ✅ Join with patientslot using zohoID
    JOIN range r ON ps.range_id = r.id  -- ✅ Join with range table
    JOIN dr d ON r."doctorID" = d.id  -- ✅ Join with doctor table using range.doctorID
    LEFT JOIN RankedAdmissions ra ON pq."patientID" = ra."patientID" AND ra.row_rank = 1  -- Exclude last admitted patient
    LEFT JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
    WHERE
        pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND pq."completedAt" IS NULL
        AND (pq.status = 'ONLINE' OR pq.status = 'JOINED' )
        AND pq."noShow" IS NULL
        AND d."accessID" = $1  -- ✅ Filter by doctor's accessID
        AND (ra."patientID" IS NULL OR ra."drId" = $1)  -- ✅ Ensures the patient is not the last admitted patient
        AND (lc.completed = false OR lc.completed IS NULL)  -- ✅ Only include patients with incomplete consultations
    ORDER BY pq."joinedCallAt" ASC, p."returningPatient" DESC, p."riskRating" ASC, pq."updatedAt" ASC
    LIMIT 1;`;

  try {
    // Begin transaction
    await client.query('BEGIN');

    // Check if the patient is already admitted - if so, don't mark as no-show
    const patientStatusCheck = `
      SELECT status
      FROM patientqueue
      WHERE "patientID" = $1
      AND "completedAt" IS NULL
      LIMIT 1;
    `;

    const patientStatus = await client.query(patientStatusCheck, [patientID]);
    if (patientStatus.rows.length > 0 && patientStatus.rows[0].status === 'ADMITTED') {
      //logger.info(`Patient ${patientID} is already ADMITTED, skipping no-show marking`);
      await client.query('COMMIT');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT_ALREADY_ADMITTED', null, true));
      return;
    }

    // Mark the current patient as no-show first
    await client.query(updatePatientAttempt, [patientID, drId]);

    // Now check if doctor already has another active admission
    const activeAdmission = await client.query(activeAdmissionCheck, [drId, patientID]);
    if (activeAdmission.rows.length > 0) {
      // Doctor already has an active admission, don't admit another patient
      //logger.info(`Doctor ${drId} already has an active admission, skipping auto-admit after no-show`);
      await client.query('COMMIT');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      return;
    }

    // Get doctor name for logging
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [drId]);
    const doctorName = doctorResult.rows[0]?.username || drId;

    const result = await client.query(nonNotifiedPatient, [drId]);
    if (result.rows?.length > 0) {
      await client.query(updatePatientAttempt, [result.rows[0].patientID, drId]);
      logger.info(
        `Doctor ${doctorName} - Admitted next patient Auto (First time): ${result.rows?.[0].patientID}, attempt: ${result.rows?.[0].attempt}`,
      );
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows[0], true));
      return;
    } else {
      logger.info(
        `Doctor ${doctorName} - No non-notified patients found from assigned patient list. Redirect Back to Online List`,
      );
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      logger.info(
        `Doctor ${doctorName} - No non-notified patients found from assigned patient list. Redirect Back to Online List`,
      );
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
    }
    await client.query('COMMIT');
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updatePatientEmail: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const newEmail = req.body.email as string;
  const patientID = req.body.patientID as string;
  const mobile = req.body.mobile as string;

  try {
    if (newEmail && newEmail.length > 0) {
      await client.query('BEGIN');

      let result;
      if (patientID && patientID.length > 0) {
        // Update using patientID if it exists
        const query = `UPDATE Patient SET email=$1 WHERE "patientID" = $2`;
        result = await client.query(query, [newEmail, patientID]);
      } else if (mobile && mobile.length > 0) {
        // Update using mobile if patientID doesn't exist but mobile does
        const query = `UPDATE Patient SET email=$1 WHERE "mobile" = $2`;
        result = await client.query(query, [newEmail, mobile]);
      } else {
        // No valid identifier provided
        await client.query('ROLLBACK');
        logger.error(`No patientID or mobile provided for email update`);
        res.status(200).send([]);
        return;
      }

      await client.query('COMMIT');
      // Check if any rows were affected by the update
      if (result.rowCount > 0) {
        res.status(200).send(result.rows);
      } else {
        logger.error(`No matching patient found`);
        res.status(200).send([]);
      }
      return;
    }
    logger.error(`No email provided for email update`);
    res.status(200).send([]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const testWarningPatient: RequestHandler = catchAll(async (_req, res) => {
  await warnPatient();
  res.status(200).send({});
});

const warnPatient = async () => {
  const client = await db.connect();
  const query = `UPDATE Consultation SET "warningSent"=$1, "warningSentAt"=CURRENT_TIMESTAMP WHERE id=$2`;

  try {
    await client.query('BEGIN');
    const gettingNextPatientToWarn = await client.query(Queries.fetchNextPatientToWarn('3'));

    // Check if there are any patients who have been notified but not warned
    if (gettingNextPatientToWarn.rows.length === 0) {
      logger.info(`No patients found who have been notified but not warned yet`);
      await client.query('COMMIT');
      return;
    }

    // Only proceed if we have enough patients to warn (need at least 3)
    if (gettingNextPatientToWarn.rows.length >= 3) {
      const warnThisPatient = gettingNextPatientToWarn.rows[2] as PatientData;
      const consultDateAndTime = warnThisPatient?.consultation?.consultationDate;
      await client.query(query, [true, warnThisPatient?.consultation?.id]);
      logger.info(`Consultation ${warnThisPatient?.consultation?.id} has been updated`);

      const headers = await ZohoAuth.getHeaders();
      const data = {
        data: [
          {
            Reschedule_Calendly_API: 'yes',
          },
        ],
      };

      const delayedNotification = {
        data: [
          {
            Delayed_Warning: 'yes',
          },
        ],
      };

      if (consultDateAndTime) {
        const consultationDate = DateTime.fromISO(consultDateAndTime, { zone: 'Australia/Sydney' });
        // Get current time in Australia/Sydney
        const currentTime = DateTime.now().setZone('Australia/Sydney');

        // Calculate difference in minutes
        const diffInMinutes = consultationDate.diff(currentTime, 'minutes').minutes;

        if (diffInMinutes > 20) {
          // Delay notification
          await axios.put(`${zohoLeadURL}/${warnThisPatient.zohoID}`, delayedNotification, { headers });
          logger.info(`Delayed Warning set for ${warnThisPatient.fullName}`);
        } else {
          // Instant notification
          await axios.put(`${zohoLeadURL}/${warnThisPatient.zohoID}`, data, { headers });
          logger.info(`Warning sent to ${warnThisPatient.fullName}`);
        }
      }
    }

    await client.query('COMMIT');
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    logger.error(`Error in warnPatient: ${error.message}`);
    // Don't rethrow the error - we don't want this to break the flow
  } finally {
    client.release();
  }
};

const callNoShowPatient = async () => {
  // Define queries outside the setTimeout to avoid recreating them
  const noShowQuery = `
    WITH LatestConsultation AS (
      SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.*
      FROM Consultation c
      JOIN Patient p ON c."patientID" = p."patientID"
      JOIN PatientSlot ps ON p."zohoID" = ps.patient_id
      JOIN Range r ON ps.range_id = r.id
      WHERE r."doctorID" IS NOT NULL  -- Only include consultations for patients with doctor assignments
      ORDER BY c."createdAt",c."consultationDate", c."patientID" DESC
    )
    SELECT p."fullName",
          p."mobile",
          p."zohoID",
          p."patientID",
          lc."queueTag",
          lc.id AS "consultationID"
    FROM PatientQueue pq
    LEFT JOIN Patient p ON p."patientID" = pq."patientID"
    LEFT JOIN LatestConsultation lc ON lc."patientID" = pq."patientID"
    WHERE pq."joinedAt" IS NULL
      AND pq.status = 'OFFLINE'
      AND pq."notificationSentDateTime" IS NOT NULL
      AND CURRENT_TIMESTAMP >= pq."notificationSentDateTime" + INTERVAL '10 minutes'
      AND lc."queueTag" IS DISTINCT FROM 'no-show'
      AND lc."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND lc."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
    ORDER BY pq."notificationSentDateTime" ASC;
  `;

  const updateConsultationTag = `UPDATE Consultation SET "queueTag" = 'no-show' WHERE id = $1`;

  const noShowData = {
    data: [
      {
        Queue_Tags: 'no-show',
      },
    ],
  };

  const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

  // Get Zoho headers outside the timeout to avoid potential auth issues
  const headers = await ZohoAuth.getHeaders();

  // Add 10 second delay before running queries
  setTimeout(async () => {
    // Get a fresh database connection inside the setTimeout callback
    let client: import('pg').PoolClient | undefined;
    try {
      client = await db.connect();

      // Begin transaction
      await client.query('BEGIN');

      const noShowResult = await client.query(noShowQuery);
      const noShowList = noShowResult.rows as NoShows[];

      if (noShowList.length < 1) {
        logger.info(`No sales team notifications sent. No Show List Size: ${noShowList.length}`);
      } else {
        for (const noShow of noShowList) {
          await client.query(updateConsultationTag, [noShow.consultationID]);
          await axios.put(`${zohoLeadURL}/${noShow.zohoID}`, noShowData, { headers });
          logger.info(`No Show notification sent to ${noShow.fullName}`);
          await delay(2000); // 2 second delay between each notification
        }
      }

      // Commit transaction
      await client.query('COMMIT');
    } catch (e) {
      // Rollback transaction if there was an error
      if (client) {
        await client.query('ROLLBACK');
      }
      const error = e as Error;
      logger.error(`Failure to call noShow patient: ${error.message}`);
    } finally {
      // Always release the client connection
      if (client) {
        client.release();
      }
    }
  }, 10000);
};

export const postPatientOrder: RequestHandler = catchAll(async (req, res) => {
  const data = req.body as PatientOrder;
  const client = await db.connect();

  const insertPatientOrderQuery = `
    INSERT INTO PatientOrder (
      "orderID", "wpUserID", "zohoContactID", "email",
      "treatmentPlanDate", "drName", "orderDateAndTime", "allowanceLeft22", "allowanceLeft29",
      "repeatLeft22", "repeatLeft29", "createdAt", "updatedAt"
    ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
  `;

  const insertOrderItemsQuery = `
    INSERT INTO OrderItems (
      "orderID", sku, "tradeName", quantity, strength, "createdAt", "updatedAt"
    ) VALUES
    ${data.items.map((_, i) => `($1, $${i * 4 + 2}, $${i * 4 + 3}, $${i * 4 + 4}, $${i * 4 + 5}, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)`).join(', ')}
  `;

  const orderItemValues = data.items.flatMap((item) => [item.sku, item.trade_name, item.quantity, item.strength]);

  try {
    await client.query('BEGIN');

    // Insert into PatientOrder
    await client.query(insertPatientOrderQuery, [
      data.order_id,
      data.wp_user_id,
      data.zoho_contact_id,
      data.email,
      data.treatmentPlanDate,
      data.drName,
      data.orderDateAndTime,
      data.allowanceLeft[22].toFixed(1),
      data.allowanceLeft[29].toFixed(1),
      data.repeatLeft[22],
      data.repeatLeft[29],
    ]);

    // Bulk Insert into OrderItems
    await client.query(insertOrderItemsQuery, [data.order_id, ...orderItemValues]);

    await client.query('COMMIT');
    logger.info(`Patient order ${data.order_id} created successfully`);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', data, true));
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    logger.error(`Error creating patient order: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getPatientOrdersByEmail: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const email = req.params.email;

  const getOrdersQuery = `
    SELECT
      po."orderID" AS order_id,
      po."wpUserID" AS wp_user_id,
      po."zohoContactID" AS zoho_contact_id,
      po."allowanceLeft",
      po."email",
      po."id",
      po."treatmentPlanDate",
      po."drName",
      po."orderDateAndTime",
      po."type",
      po."repeatLeft",
      po."collector",
      po."createdAt",
      po."updatedAt",
      po."allowanceLeft22",
      po."allowanceLeft29",
      po."repeatLeft22",
      po."repeatLeft29",
      COALESCE(
        JSON_AGG(
          JSON_BUILD_OBJECT(
            'sku', oi.sku,
            'trade_name', oi."tradeName",
            'quantity', oi.quantity,
            'strength', oi.strength
          )
        ) FILTER (WHERE oi.id IS NOT NULL), '[]'
      ) AS items
    FROM PatientOrder po
    LEFT JOIN OrderItems oi ON po."orderID" = oi."orderID"
    WHERE po."email" = $1
    GROUP BY po."orderID", po."wpUserID", po."zohoContactID", po."allowanceLeft",
             po."email", po."treatmentPlanDate", po."drName", po."type",  po."id",
             po."orderDateAndTime", po."repeatLeft", po."createdAt",  po."collector", po."updatedAt",
             po."allowanceLeft22", po."allowanceLeft29", po."repeatLeft22", po."repeatLeft29"
    ORDER BY po."orderDateAndTime" DESC;
  `;

  try {
    const result = await client.query(getOrdersQuery, [email]);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows, true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchTreatmentPlanByEmail: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const email = req.params.email;
  const query = `SELECT * FROM Treatmentplan WHERE email=$1`;

  try {
    const result = await client.query(query, [email]);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows, true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchQuestionnaireByEmail: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id;
  const query = `SELECT * FROM Questionnaire WHERE "patientID"=$1`;

  try {
    const result = await client.query(query, [id]);
    if (result.rows.length > 0) {
      const questionnaireShape = {
        ...result.rows[0],
        data: result.rows,
      };
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', questionnaireShape, true));
      return;
    }
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getDoctorsActivity: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    const query = `
      SELECT
        d.name,
        d.email,
        dt.action,
        TO_CHAR(dt."createdAt", 'YYYY-MM-DD"T"HH24:MI:SS"Z"') AS "createdAt"
      FROM dr d
      INNER JOIN doctorstartendtimer dt ON dt."drId"=d."accessID"`;

    const { rows } = await client.query(query);
    res.status(200).json({ data: rows });
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const postChatTreatmentPlan: RequestHandler = catchAll(async (req, res) => {
  const body = req.body as PatientTreatmentPlan;
  const patientID = body.patient?.patientID;
  const zohoID = body.zohoID; // Using the prescription lead's zohoID

  const client = await db.connect();

  await client.query('BEGIN');
  try {
    // Fetch patient data to get email if not provided
    let patientEmail = body.patient?.email;

    if (!patientEmail) {
      const patientResult = await client.query(`SELECT email FROM Patient WHERE "patientID" = $1`, [patientID]);

      if (patientResult.rows.length > 0) {
        patientEmail = patientResult.rows[0].email;
      }
    }

    // Update patient status
    // await client.query(
    //   `
    //   UPDATE Patient
    //   SET "drLocked" = $1, locked = $2, "updatedAt" = CURRENT_TIMESTAMP
    //   WHERE "patientID" = $3
    //   `,
    //   [null, false, patientID],
    // );

    // // Update consultation status
    // await client.query(
    //   `
    //   UPDATE Consultation
    //   SET "meetingOngoing" = $1, completed = $2, "updatedAt" = CURRENT_TIMESTAMP
    //   WHERE "patientID" = $3
    //   `,
    //   [false, true, patientID],
    // );

    // Update queue status
    await client.query(`UPDATE Patientqueue SET "noShow"=false WHERE "patientID"=$1`, [patientID]);

    // Preserve doctor notes exactly as received, only use default if undefined
    const drNotes = body.drNotes !== undefined ? body.drNotes : '';

    // Ensure we always have a doctor name
    const drName = body.drName || 'Doctor';

    // Check for existing treatment plan with the same patientID, consultationId, and email
    const checkDuplicateQuery = `
    SELECT id FROM TreatmentPlan
    WHERE "patientID" = $1
    AND "consultationId" = $2
    AND "email" = $3
    AND "outcome" = $4
    LIMIT 1
    `;

    const duplicateResult = await client.query(checkDuplicateQuery, [
      patientID,
      body.patient?.consultation?.id,
      patientEmail,
      body.outcome,
    ]);

    // If duplicate exists, log it and return early without updating Zoho
    if (duplicateResult.rows.length > 0) {
      logger.info(
        `Duplicate chat treatment plan detected for patientID: ${patientID}, consultationId: ${body.patient?.consultation?.id}, email: ${patientEmail}, outcome: ${body.outcome}`,
      );
      logger.info(`Skipping insertion of duplicate chat treatment plan and Zoho update`);

      await client.query('COMMIT');
      WebSocketManager.dispatch(TOPICS.REMOVE_ID, {
        patientID,
      });
      res.status(200).send({
        patientID,
        message: 'Treatment plan already exists',
      });
      return;
    }

    // Save treatment plan with patient email in the email column
    const treatmentPlanQuery = `
    INSERT INTO TreatmentPlan (
      "patientID",
      "drId",
      "consultationId",
      "updatedAt",
      outcome,
      "drNotes",
      "diagnosis",
      "diagnosis",
      date,
      "drName",
      "strengthAndConcentration22",
      "dosePerDay22",
      "maxDose22",
      "totalQuantity22",
      "numberOfRepeat22",
      "supplyInterval22",
      "strengthAndConcentration29",
      "dosePerDay29",
      "maxDose29",
      "totalQuantity29",
      "numberOfRepeat29",
      "supplyInterval29",
      email,
      "mentalHealthSupprtingDocument",
      "idVerified",
      "idVerified",
      "type",
      "source",
      "source",
      "createdAt"
    )
          VALUES (
        $1, $2, $3, CURRENT_TIMESTAMP, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13,
       $14, $15, $16, $17, $18, $19, $20, $21, $22, 'chat-treatmentplan', 'messenger', CURRENT_TIMESTAMP
      )
    `;

    await client.query(treatmentPlanQuery, [
      body.patient?.patientID,
      body.drId,
      body.patient?.consultation?.id,
      body.outcome,
      drNotes, // Use just the doctor notes without email data
      body.diagnosis, // Add diagnosis field
      body.diagnosis, // Add diagnosis field
      body.date,
      drName, // Use the doctor name with fallback
      body[22] ? '22%' : undefined,
      body[22]?.dosePerDay,
      body[22]?.maxDosePerDay,
      body[22]?.totalQuantity,
      body[22]?.numberOfRepeat,
      body[22]?.supplyInterval,
      body[29] ? '29%' : undefined,
      body[29]?.dosePerDay,
      body[29]?.maxDosePerDay,
      body[29]?.totalQuantity,
      body[29]?.numberOfRepeat,
      body[29]?.supplyInterval,
      patientEmail, // Use the patient's actual email address
      body.mentalHealthSupportingDocumentation,
      body.idVerified,
      body.idVerified,
    ]);

    // Prepare Zoho data
    const headers = await ZohoAuth.getHeaders();
    const strainAdvice = [body?.email?.checkedSativa, body.email?.checkedIndica, body.email?.checkedHybrid]
      .flat()
      .filter((a) => a !== undefined)
      .reduce(
        (strainAdvice, value, index) => {
          strainAdvice[`strainAdvice${index + 1}`] = value;
          return strainAdvice;
        },
        {} as { [key: string]: string },
      );

    let otherTreatment: {
      [key: string]: string;
    } = {};

    if (body?.email?.otherTreatment) {
      otherTreatment = Object.keys(body.email.otherTreatment).reduce(
        (otherTreatment, key, index) => {
          if (body.email?.otherTreatment?.[key]) {
            otherTreatment[`OtherTreatment${index + 1}`] = body.email?.otherTreatment?.[key];
          }
          return otherTreatment;
        },
        {} as { [key: string]: string },
      );
    }

    // Update Zoho prescription lead
    const data = {
      data: [
        {
          Dr_Approve_Date_Time: getFormatedZohoDate(),
          Strength_Concentration: getStrength(body),
          Dr_Trigger: body.outcome,
          Mental_Health_Supporting_Documentation: body.mentalHealthSupportingDocumentation,
          ID_Verified: body.idVerified,
          Consulting_Doctor: body.drName,
          Prescription_Date_1: getFormatedZohoDate().split('T')[0],
          Specified_Dose: body[22]?.dosePerDay,
          Maximum_Doses_per_Day: body[22]?.maxDosePerDay,
          Total_Qty_22_1: body[22]?.totalQuantity,
          Number_of_Repeats_22: body[22]?.numberOfRepeat,
          Dose_Per_Day_29: body[29]?.dosePerDay,
          Maximum_Doses_per_Day_29: body[29]?.maxDosePerDay,
          Number_of_Repeats_29: body[22]?.numberOfRepeat ? body[22]?.numberOfRepeat : body[29]?.numberOfRepeat,
          Total_Qty_29_1: body[29]?.totalQuantity,
          Dispensing_Interval_Period_1: body[22]?.supplyInterval ? body[22]?.supplyInterval : body[29]?.supplyInterval,
          Doctor_Notes: body.drNotes !== undefined ? body.drNotes : '',
          Dr_AHPRA_Number: body.drAphraNumber,
          Introduction1: body?.email?.introMessage?.intro,
          Introduction2: body?.email?.introMessage?.conclusion,
          SideEffect1: body?.email?.listTitle?.title1,
          SideEffect2: body?.email?.listTitle?.title2,
          SideEffect3: body?.email?.listTitle?.title3,
          SubSideEffect1: body?.email?.listItemText?.item1,
          SubSideEffect2: body?.email?.listItemText?.item2,
          SubSideEffect3: body?.email?.listItemText?.item3,
          Existing_Patient: 'Yes',
          ...strainAdvice,
          ...otherTreatment,
        },
      ],
    };

    if (!zohoID) {
      throw new ApiError(httpStatus.BAD_REQUEST, 'Zoho ID is required');
    }

    // Update Zoho lead with treatment plan and email content
    const result = await axios.put(`${zohoLeadURL}/${zohoID}`, data, { headers });
    if (result?.data?.data?.[0]) {
      if (result?.data?.data?.[0].status === 'error') {
        await client.query('ROLLBACK');
        throw new ApiError(httpStatus.BAD_REQUEST, result?.data?.data?.[0]);
      }

      if (result?.data?.data?.[0].status === 'success') {
        //logger.info(`Successfully updated Patient :: ${zohoID} in Zoho`);
      }
    }

    await client.query('COMMIT');

    // Notify websocket clients
    WebSocketManager.dispatch(TOPICS.REMOVE_ID, {
      patientID,
    });

    res.status(200).send({
      patientID,
      message: 'Treatment plan submitted and email sent successfully',
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;

    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const userActions: RequestHandler = catchAll(async (req, res) => {
  const action = req.body.action;
  const target = req.body.target;
  const origin = req.body.origin;
  const comment = req.body.comment;
  const response = {
    origin: origin ?? 'unknown',
    action: action,
    target: target,
    comment: comment,
  };

  req.log?.summary(JSON.stringify(response));
  res.status(200).send({});
});

export const trackDoctorLogin: RequestHandler = catchAll(async (req, res) => {
  const { doctorId, sessionId, ipAddress, userAgent } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Verify doctor exists and has doctor role (not admin)
    const doctorCheck = await client.query(
      'SELECT "accessID", name, username, role, "consultationDurationMinutes" FROM Dr WHERE "accessID" = $1',
      [doctorId],
    );

    if (doctorCheck.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Doctor not found');
    }

    const doctor = doctorCheck.rows[0];

    // Only track sessions for doctors, not admins
    if (doctor.role !== 'doctor') {
      throw new ApiError(httpStatus.FORBIDDEN, 'Session tracking is only available for doctors');
    }

    // Insert login record
    const loginQuery = `
      INSERT INTO doctor_sessions ("doctorId", "sessionId", action, timestamp, "ipAddress", "userAgent", "createdAt", "updatedAt")
      VALUES ($1, $2, 'LOGIN', CURRENT_TIMESTAMP, $3, $4, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const loginResult = await client.query(loginQuery, [
      doctorId,
      sessionId || null,
      ipAddress || null,
      userAgent || null,
    ]);

    await client.query('COMMIT');

    logger.info(
      `Doctor login tracked: ${doctor.name || doctor.username} (${doctorId}) from IP: ${ipAddress || 'unknown'}`,
    );

    logger.info(
      ` Doctor ${doctor.name || doctor.username} has logged in with ${doctor.consultationDurationMinutes} minutes allocated per consultation`,
    );

    // Integrate Zelda management when doctor logs in
    try {
      const zeldaManager = ZeldaManager.getInstance(db);
      await zeldaManager.manageZelda(doctorId, 'LOGIN');
    } catch (zeldaError) {
      logger.error(`Error in Zelda management for doctor ${doctorId}:`, zeldaError);
      // Don't fail the login tracking if Zelda management fails
    }

    // Emit WebSocket event for real-time doctor status update
    const sydneyTimestamp = await client.query(
      `SELECT TO_CHAR(CURRENT_TIMESTAMP AT TIME ZONE 'Australia/Sydney', 'YYYY-MM-DD"T"HH24:MI:SS') AS timestamp`,
    );

    WebSocketManager.dispatch(TOPICS.DOCTOR_LOGIN, {
      doctorId: doctorId,
      doctorName: doctor.name || doctor.username,
      action: 'LOGIN',
      timestamp: sydneyTimestamp.rows[0].timestamp,
      sessionId: sessionId,
    });

    res.status(200).json({
      success: true,
      data: loginResult.rows[0],
      message: 'Login tracked successfully',
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    logger.error(`Error tracking doctor login: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const trackDoctorLogout: RequestHandler = catchAll(async (req, res) => {
  const { doctorId, sessionId, sessionDuration, ipAddress, userAgent } = req.body;
  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Verify doctor exists and has doctor role (not admin)
    const doctorCheck = await client.query('SELECT "accessID", name, username, role FROM Dr WHERE "accessID" = $1', [
      doctorId,
    ]);

    if (doctorCheck.rows.length === 0) {
      throw new ApiError(httpStatus.NOT_FOUND, 'Doctor not found');
    }

    const doctor = doctorCheck.rows[0];

    // Only track sessions for doctors, not admins
    if (doctor.role !== 'doctor') {
      throw new ApiError(httpStatus.FORBIDDEN, 'Session tracking is only available for doctors');
    }

    // Calculate session duration if not provided
    let calculatedDuration = sessionDuration;
    if (!calculatedDuration && sessionId) {
      // Find the most recent login for this session
      const lastLoginQuery = `
        SELECT timestamp FROM doctor_sessions
        WHERE "doctorId" = $1 AND "sessionId" = $2 AND action = 'LOGIN'
        ORDER BY timestamp DESC
        LIMIT 1
      `;
      const lastLogin = await client.query(lastLoginQuery, [doctorId, sessionId]);

      if (lastLogin.rows.length > 0) {
        const loginTime = new Date(lastLogin.rows[0].timestamp);
        const logoutTime = new Date();
        calculatedDuration = Math.floor((logoutTime.getTime() - loginTime.getTime()) / 1000);
      }
    }

    // Insert logout record
    const logoutQuery = `
      INSERT INTO doctor_sessions ("doctorId", "sessionId", action, timestamp, "sessionDuration", "ipAddress", "userAgent", "createdAt", "updatedAt")
      VALUES ($1, $2, 'LOGOUT', CURRENT_TIMESTAMP, $3, $4, $5, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
      RETURNING *
    `;

    const logoutResult = await client.query(logoutQuery, [
      doctorId,
      sessionId || null,
      calculatedDuration || null,
      ipAddress || null,
      userAgent || null,
    ]);

    await client.query('COMMIT');

    const durationText = calculatedDuration
      ? `(${Math.floor(calculatedDuration / 60)}m ${calculatedDuration % 60}s)`
      : '';
    logger.info(
      `Doctor logout tracked: ${doctor.name || doctor.username} (${doctorId}) ${durationText} from IP: ${ipAddress || 'unknown'}`,
    );

    // Emit WebSocket event for real-time doctor status update
    const sydneyTimestamp = await client.query(
      `SELECT TO_CHAR(CURRENT_TIMESTAMP AT TIME ZONE 'Australia/Sydney', 'YYYY-MM-DD"T"HH24:MI:SS') AS timestamp`,
    );

    WebSocketManager.dispatch(TOPICS.DOCTOR_LOGOUT, {
      doctorId: doctorId,
      doctorName: doctor.name || doctor.username,
      action: 'LOGOUT',
      timestamp: sydneyTimestamp.rows[0].timestamp,
      sessionId: sessionId,
      sessionDuration: calculatedDuration,
    });

    res.status(200).json({
      success: true,
      data: logoutResult.rows[0],
      message: 'Logout tracked successfully',
    });
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    logger.error(`Error tracking doctor logout: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getDoctorSessionHistory: RequestHandler = catchAll(async (req, res) => {
  const { doctorId } = req.params;
  const { startDate, endDate, limit = 50, offset = 0 } = req.query;
  const client = await db.connect();

  try {
    let query = `
      SELECT
        ds.id,
        ds."doctorId",
        ds."sessionId",
        ds.action,
        TO_CHAR(ds.timestamp AT TIME ZONE 'Australia/Sydney', 'YYYY-MM-DD"T"HH24:MI:SS') AS timestamp,
        ds."sessionDuration",
        ds."ipAddress",
        ds."userAgent",
        TO_CHAR(ds."createdAt" AT TIME ZONE 'Australia/Sydney', 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        d.name AS "doctorName",
        d.username AS "doctorUsername"
      FROM doctor_sessions ds
      INNER JOIN Dr d ON ds."doctorId" = d."accessID"
      WHERE 1=1
    `;

    const queryParams: unknown[] = [];
    let paramIndex = 1;

    // Add doctor filter if specified
    if (doctorId && doctorId !== 'all') {
      query += ` AND ds."doctorId" = $${paramIndex}`;
      queryParams.push(doctorId);
      paramIndex++;
    }

    // Add date range filters (compare UTC timestamps directly)
    if (startDate) {
      query += ` AND ds.timestamp >= $${paramIndex}::timestamptz`;
      queryParams.push(startDate);
      paramIndex++;
    }

    if (endDate) {
      query += ` AND ds.timestamp <= $${paramIndex}::timestamptz`;
      queryParams.push(endDate);
      paramIndex++;
    }

    // Add ordering and pagination
    query += ` ORDER BY ds.timestamp DESC`;
    query += ` LIMIT $${paramIndex} OFFSET $${paramIndex + 1}`;
    queryParams.push(parseInt(limit as string), parseInt(offset as string));

    const result = await client.query(query, queryParams);

    // Get total count for pagination
    let countQuery = `
      SELECT COUNT(*) as total
      FROM doctor_sessions ds
      WHERE 1=1
    `;
    const countParams: unknown[] = [];
    let countParamIndex = 1;

    if (doctorId && doctorId !== 'all') {
      countQuery += ` AND ds."doctorId" = $${countParamIndex}`;
      countParams.push(doctorId);
      countParamIndex++;
    }

    if (startDate) {
      countQuery += ` AND ds.timestamp >= $${countParamIndex}::timestamptz`;
      countParams.push(startDate);
      countParamIndex++;
    }

    if (endDate) {
      countQuery += ` AND ds.timestamp <= $${countParamIndex}::timestamptz`;
      countParams.push(endDate);
    }

    const countResult = await client.query(countQuery, countParams);
    const total = parseInt(countResult.rows[0].total);

    res.status(200).json({
      success: true,
      data: result.rows,
      pagination: {
        total,
        limit: parseInt(limit as string),
        offset: parseInt(offset as string),
        hasMore: parseInt(offset as string) + parseInt(limit as string) < total,
      },
    });
  } catch (e) {
    const error = e as Error;
    logger.error(`Error fetching doctor session history: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const getDoctorOnlineStatus: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();

  try {
    // Get only active doctors who have logged in at least once today
    const query = `
      WITH todays_sessions AS (
        SELECT DISTINCT ON (ds."doctorId")
          ds."doctorId",
          ds.action,
          ds.timestamp,
          ds."sessionId"
        FROM doctor_sessions ds
        INNER JOIN Dr d ON ds."doctorId" = d."accessID"
        WHERE d.role = 'doctor'
          AND d.status = 'active'
          AND ds.timestamp ${timeZone} >= (NOW() ${timeZone})::DATE
          AND ds.timestamp ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
        ORDER BY ds."doctorId", ds.timestamp DESC
      ),
      latest_sessions AS (
        SELECT DISTINCT ON (ds."doctorId")
          ds."doctorId",
          ds.action,
          ds.timestamp,
          ds."sessionId"
        FROM doctor_sessions ds
        INNER JOIN Dr d ON ds."doctorId" = d."accessID"
        WHERE d.role = 'doctor'
          AND d.status = 'active'
        ORDER BY ds."doctorId", ds.timestamp DESC
      )
      SELECT
        d."accessID" as "doctorId",
        d.name as "doctorName",
        d.username as "doctorUsername",
        COALESCE(ls.action, 'LOGOUT') as "currentStatus",
        TO_CHAR(ls.timestamp AT TIME ZONE 'Australia/Sydney', 'YYYY-MM-DD"T"HH24:MI:SS') as "lastActivity",
        ls."sessionId",
        CASE WHEN ts."doctorId" IS NOT NULL THEN true ELSE false END as "loggedInToday"
      FROM Dr d
      INNER JOIN todays_sessions ts ON d."accessID" = ts."doctorId"  -- Only doctors who logged in today
      LEFT JOIN latest_sessions ls ON d."accessID" = ls."doctorId"
      WHERE d.role = 'doctor'
        AND d.status = 'active'
      ORDER BY d.name, d.username;
    `;

    const result = await client.query(query);

    // Transform the data to include online status
    const doctorStatuses = result.rows.map((row) => ({
      doctorId: row.doctorId,
      doctorName: row.doctorName || row.doctorUsername,
      isOnline: row.currentStatus === 'LOGIN',
      lastActivity: row.lastActivity,
      sessionId: row.sessionId,
      loggedInToday: row.loggedInToday,
    }));

    res.status(200).json({
      success: true,
      data: doctorStatuses,
    });
  } catch (e) {
    const error = e as Error;
    logger.error(`Error fetching doctor online status: ${error.message}`);
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchNextPatientToConsult: RequestHandler = catchAll(async (req, res) => {
  // Try to get doctorID from query parameter first
  const doctorID = req.query.doctorID as string;

  if (!doctorID) {
    //logger.error('Doctor ID not found in request');
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
    return; // Add return statement to prevent unnecessary database connection
    //throw new ApiError(httpStatus.BAD_REQUEST, 'Doctor ID is required');
  }

  // Try to find patients assigned to this specific doctor
  const assignedPatientQueue = `
    SELECT
    pq.*
    FROM
      PatientQueue pq
    WHERE (pq.status = 'ONLINE' OR pq.status ='JOINED')
    AND pq."notificationSent" = 'true'
    AND pq."completedAt" IS NULL
    AND pq.attempt <= 0
    AND pq."assignedDoctorID" = $1
    ORDER BY
      pq.attempt ASC, pq."joinedCallAt" ASC, pq."joinedAt" ASC
    LIMIT 1;
  `;

  // Only open the database connection after validating the doctorID
  const client = await db.connect();

  try {
    // First try to find patients assigned to this doctor
    const assignedResult = await client.query(assignedPatientQueue, [doctorID]);

    if (assignedResult.rows.length > 0) {
      //logger.info(`Found patient ${assignedResult.rows[0].patientID} assigned to doctor ${doctorUsername} (ID: ${doctorID})`);
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', assignedResult.rows, true));
      return;
    }

    //logger.info(`No patients available for doctor ${doctorUsername} (ID: ${doctorID})`);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchPatientsWithExceededAttempts: RequestHandler = catchAll(async (_req, res) => {
  const client = await db.connect();
  const timeZone = "AT TIME ZONE 'UTC' AT TIME ZONE 'Australia/Sydney'";

  const query = `
    SELECT
      p."fullName",
      pq."patientID",
      pq.status,
      TO_CHAR((pq."createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
      TO_CHAR((pq."updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
      TO_CHAR((pq."joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
      pq."notificationSent",
      TO_CHAR((pq."notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
      TO_CHAR((pq."leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
      TO_CHAR((pq."joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
      TO_CHAR((pq."admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
      TO_CHAR((pq."completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
      TO_CHAR((pq."callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
      TO_CHAR((pq."confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
      TO_CHAR((pq."firstTimeJoined" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "firstTimeJoined",
      pq.attempt,
      pq."noShow",
      pq.allowed
    FROM
      PatientQueue pq
    JOIN
      Patient p ON pq."patientID" = p."patientID"
    WHERE
      pq."completedAt" IS NULL
      AND pq.attempt > 0
    ORDER BY
      pq."updatedAt" DESC
  `;

  try {
    const result = await client.query(query);
    res.status(200).json(result.rows);
  } catch (error) {
    console.error('Error fetching patients with exceeded attempts:', error);
    res.status(500).json({ error: 'Failed to fetch patients with exceeded attempts' });
  } finally {
    client.release();
  }
});

export const resetPatientAttempts: RequestHandler = catchAll(async (req, res) => {
  const { id } = req.params;
  const client = await db.connect();
  const timeZone = "AT TIME ZONE 'UTC' AT TIME ZONE 'Australia/Sydney'";

  try {
    await client.query('BEGIN');

    // Update PatientQueue table
    const queueQuery = `
      UPDATE PatientQueue
      SET attempt = 0, "noShow" = false, "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $1 AND "completedAt" IS NULL
      RETURNING
        "patientID",
        status,
        TO_CHAR(("createdAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "createdAt",
        TO_CHAR(("updatedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "updatedAt",
        TO_CHAR(("joinedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedAt",
        "notificationSent",
        TO_CHAR(("notificationSentDateTime" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "notificationSentDateTime",
        TO_CHAR(("leftAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "leftAt",
        TO_CHAR(("joinedCallAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "joinedCallAt",
        TO_CHAR(("admittedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "admittedAt",
        TO_CHAR(("completedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "completedAt",
        TO_CHAR(("callEndedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "callEndedAt",
        TO_CHAR(("confirmedAt" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "confirmedAt",
        TO_CHAR(("firstTimeJoined" ${timeZone}), 'YYYY-MM-DD"T"HH24:MI:SS') AS "firstTimeJoined",
        attempt,
        "noShow",
        allowed
    `;

    const queueResult = await client.query(queueQuery, [id]);

    if (queueResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(404).json({ error: 'Patient not found or already completed' });
      return;
    }

    // Update Consultation table for the patient's most recent uncompleted consultation
    const consultationQuery = `
      UPDATE Consultation
      SET "updatedAt" = CURRENT_TIMESTAMP
      WHERE id = (
        SELECT id
        FROM Consultation
        WHERE "patientID" = $1
        AND completed = FALSE
        ORDER BY "consultationDate" DESC
        LIMIT 1
      )
    `;

    await client.query(consultationQuery, [id]);
    await client.query('COMMIT');

    res.status(200).json(queueResult.rows[0]);
  } catch (error) {
    await client.query('ROLLBACK');
    console.error('Error resetting patient attempts:', error);
    res.status(500).json({ error: 'Failed to reset patient attempts' });
  } finally {
    client.release();
  }
});

export const updatePatientZohoIdByEmail: RequestHandler = catchAll(async (req, res) => {
  const { email, zohoId } = req.body;

  if (!email || !zohoId) {
    res.status(400).send({
      success: false,
      message: 'Email and zohoId are required',
    });
    return;
  }

  const client = await db.connect();

  try {
    // First check if patient exists with the email
    const checkQuery = `SELECT id, "patientID", "zohoID" FROM patient WHERE email = $1`;
    const checkResult = await client.query(checkQuery, [email.toLowerCase()]);

    if (!checkResult.rows || checkResult.rows.length === 0) {
      res.status(404).send({
        success: false,
        message: 'Patient not found with the specified email',
      });
      return;
    }

    // Update the patient's zohoId
    const updateQuery = `
      UPDATE patient
      SET "zohoID" = $1,
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE email = $2
      RETURNING id, "patientID", "zohoID", email
    `;

    const updateResult = await client.query(updateQuery, [zohoId, email.toLowerCase()]);

    if (updateResult.rows && updateResult.rows.length > 0) {
      res.status(200).send({
        success: true,
        message: 'Patient zohoId updated successfully',
        data: updateResult.rows[0],
      });
    } else {
      res.status(500).send({
        success: false,
        message: 'Failed to update patient zohoId',
      });
    }
  } catch (error) {
    console.error('Error updating patient zohoId:', error);
    res.status(500).send({
      success: false,
      message: 'Internal server error',
    });
  } finally {
    client.release();
  }
});

export const fetchHealthcheckByPatientId: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id;

  try {
    // First get the patient's email using the patientID
    const patientQuery = `SELECT email FROM Patient WHERE "patientID"=$1`;
    const patientResult = await client.query(patientQuery, [id]);

    if (patientResult.rows.length === 0) {
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
      return;
    }

    const email = patientResult.rows[0].email;

    // Now query the healthcheck data using the email
    const healthcheckQuery = `
      SELECT
        id,
        question,
        answers,
        "createdAt",
        "updatedAt",
        "type",
        "healthCheckID",
        email
      FROM healthcheck
      WHERE email=$1
      ORDER BY "createdAt" DESC
    `;

    const result = await client.query(healthcheckQuery, [email]);

    if (result.rows.length > 0) {
      // Group the healthcheck entries by healthCheckID
      const healthchecksByID = result.rows.reduce((acc, curr) => {
        const healthCheckID = curr.healthCheckID;
        if (!acc[healthCheckID]) {
          acc[healthCheckID] = [];
        }
        acc[healthCheckID].push(curr);
        return acc;
      }, {});

      // Convert to the expected format
      const healthchecks = Object.keys(healthchecksByID).map((healthCheckID) => {
        const entries = healthchecksByID[healthCheckID];
        return {
          id: healthCheckID,
          form: entries,
          createdAt: entries[0].createdAt,
          updatedAt: entries[0].updatedAt,
          type: 'healthCheck',
          email: entries[0].email,
        };
      });

      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', healthchecks, true));
      return;
    }

    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const tpConfirmedPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  const patientID = req.params.id;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "confirmedAt", "consultationId", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP, $4, $5)
    ON CONFLICT ("patientID", "consultationId") 
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "confirmedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
    WHERE 
      PatientQueue."patientID" = $1 AND PatientQueue."consultationId" = $4
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  // Fallback query for when no consultation ID is found
  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "confirmedAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "confirmedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
  WITH upserted AS (
    INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "confirmedAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Get the PatientQueue record with today's consultation
    const { consultationId, assignedDoctorID } = await getPatientQueueWithTodayConsultation(patientID, client);

    let rows;

    if (consultationId) {
      // If we have a consultation ID, use it to update the specific record
      logger.info(`Found consultation ${consultationId} for patient ${patientID} - updating specific record`);
      const result = await client.query(query, [
        patientID,
        'CONFIRMED',
        patient?.email,
        consultationId,
        assignedDoctorID,
      ]);
      rows = result.rows;
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID} - using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'CONFIRMED', patient?.email]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'CONFIRMED', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status CONFIRMED`);

    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const callEndedPatientWaitingQueue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();

  const patientID = req.params.id;

  const query = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "callEndedAt", "consultationId", "assignedDoctorID")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP, $4, $5)
    ON CONFLICT ("patientID", "consultationId") 
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "callEndedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status",
      "assignedDoctorID" = COALESCE(EXCLUDED."assignedDoctorID", PatientQueue."assignedDoctorID")
    WHERE 
      PatientQueue."patientID" = $1 AND PatientQueue."consultationId" = $4
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  // Fallback query for when no consultation ID is found
  const fallbackQuery = `
  WITH upserted AS (
    INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "status", email, "callEndedAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    ON CONFLICT ("patientID")
    DO UPDATE SET
      "updatedAt" = CURRENT_TIMESTAMP,
      "callEndedAt" = CURRENT_TIMESTAMP,
      "status" = EXCLUDED."status"
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt",
      "assignedDoctorID",
      "consultationId"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  const patientQueueDetails = `
  WITH upserted AS (
    INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "callEndedAt")
    VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
    RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      ("confirmedAt" ${timeZone}) AS "confirmedAt",
      ("callEndedAt" ${timeZone}) AS "callEndedAt"
  )
  SELECT
    u.*,
    p."fullName"
  FROM upserted u
  JOIN Patient p ON u."patientID" = p."patientID";
`;

  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Get the PatientQueue record with today's consultation
    const { consultationId, assignedDoctorID } = await getPatientQueueWithTodayConsultation(patientID, client);

    let rows;

    if (consultationId) {
      // If we have a consultation ID, use it to update the specific record
      logger.info(`Found consultation ${consultationId} for patient ${patientID} - updating specific record`);
      const result = await client.query(query, [patientID, 'ENDED', patient?.email, consultationId, assignedDoctorID]);
      rows = result.rows;
    } else {
      // Fallback to the original behavior if no consultation ID is found
      logger.info(`No consultation found for patient ${patientID} - using fallback query`);
      const result = await client.query(fallbackQuery, [patientID, 'ENDED', patient?.email]);
      rows = result.rows;
    }

    await client.query(patientQueueDetails, [patientID, 'ENDED', patient?.email]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status ENDED`);

    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

/**
 * Helper function to get the PatientQueue record with the consultationId for today's consultation
 * @param patientID The patient ID
 * @param client The database client
 * @returns Promise with the consultationId and assignedDoctorID for today's consultation
 */
const getPatientQueueWithTodayConsultation = async (patientID: string, client: PoolClient) => {
  const query = `
    SELECT pq."consultationId", pq."assignedDoctorID" 
    FROM PatientQueue pq
    LEFT JOIN Consultation c ON pq."consultationId" = c.id
    WHERE pq."patientID" = $1
      AND (
        c."consultationDate" ${timeZone} >= (NOW() ${timeZone})::DATE
        AND c."consultationDate" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
        OR pq."consultationId" IS NULL
      )
    ORDER BY c."consultationDate" DESC
    LIMIT 1
  `;

  const result = await client.query(query, [patientID]);
  const consultationId = result.rows.length > 0 ? result.rows[0].consultationId : null;
  const assignedDoctorID = result.rows.length > 0 ? result.rows[0].assignedDoctorID : null;

  return { consultationId, assignedDoctorID };
};

// Helper function to check patient daily admission count
const checkPatientDailyAdmissionCount = async (client: PoolClient, patientID: string): Promise<number> => {
  const query = `
    SELECT COUNT(*) as admission_count
    FROM PatientQueueDetails
    WHERE "patientID" = $1
    AND "admittedAt" IS NOT NULL
    AND "admittedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
    AND "admittedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
  `;
  const result = await client.query(query, [patientID]);
  return parseInt(result.rows[0].admission_count, 10);
};

// Helper function to mark patient as no-show for excessive admissions
const markPatientAsNoShowForExcessiveAdmissions = async (client: PoolClient, patientID: string, email: string) => {
  const timeZone = "AT TIME ZONE 'UTC' AT TIME ZONE 'Australia/Sydney'";

  // Update PatientQueue
  const updateQuery = `
    UPDATE PatientQueue
    SET "noShow"=true, attempt = 1, "updatedAt"=CURRENT_TIMESTAMP
    WHERE "patientID"=$1
    RETURNING *
  `;
  await client.query(updateQuery, [patientID]);

  // Insert into PatientQueueDetails
  const patientQueueDetails = `
    WITH upserted AS (
      INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email)
      VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, 'NO-SHOW', $2)
      RETURNING
        "patientID",
        "status",
        ("createdAt" ${timeZone}) AS "createdAt",
        ("updatedAt" ${timeZone}) AS "updatedAt",
        ("joinedAt" ${timeZone}) AS "joinedAt",
        "notificationSent",
        ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
        ("leftAt" ${timeZone}) AS "leftAt",
        ("completedAt" ${timeZone}) AS "completedAt",
        ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
        ("admittedAt" ${timeZone}) AS "admittedAt"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID"
  `;
  await client.query(patientQueueDetails, [patientID, email]);
};

export const postPatientAdmission: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.body.patientID as string;
  const drID = req.body.drID as string;

  const client = await db.connect();

  // INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "joinedAt", "status", "completedAt")
  //   VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, CURRENT_TIMESTAMP)
  //   ON CONFLICT ("patientID")
  //   DO UPDATE SET
  //     "updatedAt" = CURRENT_TIMESTAMP,
  //     "completedAt" = CURRENT_TIMESTAMP,
  //     "status" = EXCLUDED."status"

  const timeZone = "AT TIME ZONE 'UTC' AT TIME ZONE 'Australia/Sydney'";

  const queueQuery = `
    WITH upserted AS (
      INSERT INTO PatientQueue ("patientID", "createdAt", "updatedAt", "admittedAt", "status", email, "consultedDoctorID")
      VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, $4)
      ON CONFLICT ("patientID")
      DO UPDATE SET
        "updatedAt" = CURRENT_TIMESTAMP,
        "admittedAt" = EXCLUDED."admittedAt",
        "status" = EXCLUDED."status",
        "consultedDoctorID" = EXCLUDED."consultedDoctorID"
      RETURNING
      "patientID",
      "status",
      ("createdAt" ${timeZone}) AS "createdAt",
      ("updatedAt" ${timeZone}) AS "updatedAt",
      ("joinedAt" ${timeZone}) AS "joinedAt",
      "notificationSent",
      ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
      ("leftAt" ${timeZone}) AS "leftAt",
      ("completedAt" ${timeZone}) AS "completedAt",
      ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
      ("admittedAt" ${timeZone}) AS "admittedAt",
      "assignedDoctorID",
      "consultedDoctorID"
    )
    SELECT
      u.*,
      p."fullName"
    FROM upserted u
    JOIN Patient p ON u."patientID" = p."patientID";
  `;

  const patientQueueDetails = `WITH upserted AS (
  INSERT INTO PatientQueueDetails ("patientID", "createdAt", "updatedAt", "status", email, "admittedAt")
  VALUES ($1, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, $2, $3, CURRENT_TIMESTAMP)
RETURNING
    "patientID",
    "status",
    ("createdAt" ${timeZone}) AS "createdAt",
    ("updatedAt" ${timeZone}) AS "updatedAt",
    ("joinedAt" ${timeZone}) AS "joinedAt",
    "notificationSent",
    ("notificationSentDateTime" ${timeZone}) AS "notificationSentDateTime",
    ("leftAt" ${timeZone}) AS "leftAt",
    ("completedAt" ${timeZone}) AS "completedAt",
    ("joinedCallAt" ${timeZone}) AS "joinedCallAt",
    ("admittedAt" ${timeZone}) AS "admittedAt"

    )
SELECT
  u.*,
  p."fullName"
FROM upserted u
JOIN Patient p ON u."patientID" = p."patientID";
`;

  const admissionQuery = `
  INSERT INTO Admission ("patientID", "createdAt", "admitted", email, "drId") VALUES ($1, CURRENT_TIMESTAMP, $2, $3, $4 )
  `;
  try {
    await client.query('BEGIN');
    const existingPatient = await client.query(`SELECT * FROM Patient WHERE "patientID"=$1`, [patientID]);
    const patient = existingPatient.rows[0] as PatientData;

    // Check if patient is already marked as no-show with exceeded attempts
    const patientStatus = await client.query(`SELECT "noShow", attempt FROM PatientQueue WHERE "patientID" = $1`, [
      patientID,
    ]);

    if (patientStatus.rows.length > 0 && patientStatus.rows[0].noShow === true && patientStatus.rows[0].attempt >= 1) {
      // Patient already exceeded admission limit, don't admit
      await client.query('COMMIT');
      logger.info(
        `Admission blocked for patient ${patientID} - noShow: true, attempts: ${patientStatus.rows[0].attempt}`,
      );

      // Log user action for audit trail
      req.log?.summary(
        JSON.stringify({
          origin: 'System',
          action: 'ADMISSION_BLOCKED',
          target: patientID,
          comment: `Patient blocked - noShow: true, attempts: ${patientStatus.rows[0].attempt}`,
        }),
      );

      logger.info(
        `Admission blocked for patient ${patientID} - noShow: true, attempts: ${patientStatus.rows[0].attempt}`,
      );

      // Log user action for audit trail
      req.log?.summary(
        JSON.stringify({
          origin: 'System',
          action: 'ADMISSION_BLOCKED',
          target: patientID,
          comment: `Patient blocked - noShow: true, attempts: ${patientStatus.rows[0].attempt}`,
        }),
      );

      res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT_EXCEEDED_ADMISSION_LIMIT', null, true));
      return;
    }

    const { rows } = await client.query(queueQuery, [patientID, 'ADMITTED', patient?.email, drID]);
    await client.query(patientQueueDetails, [patientID, 'ADMITTED', patient?.email]);
    await client.query(admissionQuery, [patientID, true, patient?.email, drID]);

    // Check if this patient now has more than 2 admissions today
    const admissionCount = await checkPatientDailyAdmissionCount(client, patientID);

    if (admissionCount > 2) {
      // Mark as no-show with attempt = 1 to exceed limit
      await markPatientAsNoShowForExcessiveAdmissions(client, patientID, patient?.email || '');
      logger.info(`Patient ${patientID} marked as no-show due to ${admissionCount} admissions today`);

      // Dispatch WebSocket update for the no-show status
      WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, {
        patientID,
        status: 'NO-SHOW',
        noShow: true,
        attempt: 1,
      });
    }

    // Mark patient as consulted by this doctor - inline queries from markPatientAsConsulted function
    const updatePatientQueueQuery = `
      UPDATE PatientQueue
      SET "consultedDoctorID" = $1,
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $2
      RETURNING *
    `;

    client.query(updatePatientQueueQuery, [drID, patientID]);

    // Update status in DoctorQueue
    const updateDoctorQueueQuery = `
      UPDATE DoctorQueue
      SET status = 'CONSULTED',
          "updatedAt" = CURRENT_TIMESTAMP
      WHERE "patientID" = $1 AND "doctorID" = $2
      RETURNING *
    `;

    await client.query(updateDoctorQueueQuery, [patientID, drID]);

    await client.query('COMMIT');
    WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, rows[0]);
    logger.info(`Updated ${patientID} with Status ADMITTED`);

    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updateAdmission: RequestHandler = catchAll(async (req, res) => {
  // TODO
  // update latest admission to admitted = false
  const client = await db.connect();

  const patientID = req.params.id;
  const status = req.body.status;
  const query = `
    UPDATE Admission
    SET "admitted" = $1, "updatedAt" = CURRENT_TIMESTAMP
    WHERE "patientID" = $2
  `;

  try {
    await client.query('BEGIN');
    const { rows } = await client.query(query, [status, patientID]);
    await client.query('COMMIT');
    res.status(200).send(rows[0]);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

// Test endpoint to check patient admission count (for testing purposes)
export const testPatientAdmissionCount: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;
  const client = await db.connect();

  try {
    const admissionCount = await checkPatientDailyAdmissionCount(client, patientID);

    // Also get the admission details for debugging
    const admissionDetails = await client.query(
      `
      SELECT
        "patientID",
        "status",
        "admittedAt" AT TIME ZONE 'Australia/Sydney' as "admittedAt_sydney",
        "createdAt" AT TIME ZONE 'Australia/Sydney' as "createdAt_sydney"
      FROM PatientQueueDetails
      WHERE "patientID" = $1
      AND "admittedAt" IS NOT NULL
      AND "admittedAt" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
      AND "admittedAt" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
      ORDER BY "admittedAt" DESC
    `,
      [patientID],
    );

    res.status(200).send({
      patientID,
      admissionCount,
      shouldMarkAsNoShow: admissionCount > 2,
      admissionDetails: admissionDetails.rows,
      currentTime: new Date().toISOString(),
      sydneyTime: new Date().toLocaleString('en-AU', { timeZone: 'Australia/Sydney' }),
    });
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const updatePatientWithTechIssue: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  // Get the patientID from request body
  const patientID = req.body.data.patientID as string;
  const drId = req.body.drId as string;

  // Check if the doctor already has an active admission
  const activeAdmissionCheck = `
    SELECT 1
    FROM patientqueue pq
    WHERE pq."completedAt" IS NULL
    AND pq.status = 'ADMITTED'
    AND pq."consultedDoctorID" = $1  -- Check for this specific doctor
    AND pq."patientID" != $2  -- Exclude the current patient being marked as tech issue
    AND pq."updatedAt" > (CURRENT_TIMESTAMP - INTERVAL '31 seconds')  -- Only consider admissions less than 31 seconds old
    LIMIT 1;
  `;

  const updatePatientTechIssue = `
    UPDATE Patientqueue
    SET
      "tech_issue" = true,
      "status" = 'AWAY',
      "updatedAt" = CURRENT_TIMESTAMP,
    UPDATE Patientqueue
    SET
      "tech_issue" = true,
      "status" = 'AWAY',
      "updatedAt" = CURRENT_TIMESTAMP,
      "consultedDoctorID" = $2,
      "callEndedAt" = COALESCE("callEndedAt", CURRENT_TIMESTAMP)
    WHERE "patientID" = $1
    WHERE "patientID" = $1
    RETURNING *;
  `;

  // Updated query to follow the same approach as getNextPatientsForDoctor
  // Now properly filters by doctor assignment through patientslot and range tables
  const nonNotifiedPatient = `
   WITH RankedAdmissions AS (
    SELECT
        "email",
        "patientID",
        "drId",
        ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
    FROM
        Admission
      ),
      LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      )
    SELECT pq.*
    FROM patientqueue pq
    JOIN patient p ON pq."patientID" = p."patientID"  -- ✅ Join by patientID instead of email
    JOIN patientslot ps ON p."zohoID" = ps.patient_id  -- ✅ Join with patientslot using zohoID
    JOIN range r ON ps.range_id = r.id  -- ✅ Join with range table
    JOIN dr d ON r."doctorID" = d.id  -- ✅ Join with doctor table using range.doctorID
    LEFT JOIN RankedAdmissions ra ON pq."patientID" = ra."patientID" AND ra.row_rank = 1  -- Exclude last admitted patient
    LEFT JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
    WHERE
        pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND pq."notificationSentDateTime" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        AND pq."completedAt" IS NULL
        AND (pq.status = 'ONLINE' OR pq.status = 'JOINED' )
        AND (pq."tech_issue" IS NULL OR pq."tech_issue" = false)
        AND d."accessID" = $1  -- ✅ Filter by doctor's accessID
        AND (ra."patientID" IS NULL OR ra."drId" = $1)  -- ✅ Ensures the patient is not the last admitted patient
        AND (lc.completed = false OR lc.completed IS NULL)  -- ✅ Only include patients with incomplete consultations
    ORDER BY pq."joinedCallAt" ASC, p."returningPatient" DESC, p."riskRating" ASC, pq."updatedAt" ASC
    LIMIT 1;`;

  // Updated query to follow the same approach as getNextPatientsForDoctor
  // Now properly filters by doctor assignment through patientslot and range tables
  const notifiedPatient = `
    WITH RankedAdmissions AS (
      SELECT
          "email",
          "patientID",
          "drId",
          ROW_NUMBER() OVER (PARTITION BY "drId" ORDER BY "createdAt" DESC) AS row_rank
      FROM
          Admission
      ),
      LatestConsultation AS (
        SELECT DISTINCT ON (c."createdAt",c."consultationDate",c."patientID") c.id, c."patientID", c."consultationDate", c."notificationSent", c.completed
        FROM Consultation c
        WHERE c."consultationDate" AT TIME ZONE 'Australia/Sydney' >= (NOW() AT TIME ZONE 'Australia/Sydney')::DATE
        AND c."consultationDate" AT TIME ZONE 'Australia/Sydney' < ((NOW() AT TIME ZONE 'Australia/Sydney')::DATE + INTERVAL '1 day')
        ORDER BY c."createdAt", c."consultationDate", c."patientID" DESC
      )
      SELECT pq.*
      FROM patientqueue pq
      JOIN patient p ON pq."patientID" = p."patientID"  -- ✅ Join by patientID instead of email
      JOIN patientslot ps ON p."zohoID" = ps.patient_id  -- ✅ Join with patientslot using zohoID
      JOIN range r ON ps.range_id = r.id  -- ✅ Join with range table
      JOIN dr d ON r."doctorID" = d.id  -- ✅ Join with doctor table using range.doctorID
      LEFT JOIN RankedAdmissions ra ON pq."patientID" = ra."patientID" AND ra.row_rank = 1  -- Exclude last admitted patient
      LEFT JOIN LatestConsultation lc ON p."patientID" = lc."patientID"
      WHERE
          pq."notificationSentDateTime" ${timeZone} >= (NOW() ${timeZone})::DATE
          AND pq."notificationSentDateTime" ${timeZone} < ((NOW() ${timeZone})::DATE + INTERVAL '1 day')
          AND pq."completedAt" IS NULL
          AND (pq.status = 'AWAY' OR pq.status = 'ONLINE' OR pq.status = 'ENDED' OR pq.status = 'ADMITTED' OR pq.status = 'JOINED')
          AND pq."tech_issue" IS NOT NULL
          AND pq."tech_issue" = true
          AND pq."patientID" != $2  -- ✅ Exclude the patient that was just marked as tech issue
          AND pq."patientID" != $2  -- ✅ Exclude the patient that was just marked as tech issue
          AND d."accessID" = $1  -- ✅ Filter by doctor's accessID
          AND (ra."patientID" IS NULL OR ra."drId" = $1)   -- ✅ Ensures the patient is not the last admitted patient
          AND (pq."assignedDoctorID" = $1 OR pq."consultedDoctorID" = $1)  -- ✅ Only allow the doctor who initially attempted the consult to reattempt
          AND (lc.completed = false OR lc.completed IS NULL)  -- ✅ Only include patients with incomplete consultations
      ORDER BY pq."joinedCallAt" ASC, p."returningPatient" DESC, p."riskRating" ASC, pq."updatedAt" ASC
      LIMIT 1;`;

  try {
    // Begin transaction
    await client.query('BEGIN');

    // Check if the patient is already admitted - if so, don't mark as tech issue
    const patientStatusCheck = `
      SELECT status
      FROM patientqueue
      WHERE "patientID" = $1
      AND "completedAt" IS NULL
      LIMIT 1;
    `;

    const patientStatus = await client.query(patientStatusCheck, [patientID]);
    if (patientStatus.rows.length > 0 && patientStatus.rows[0].status === 'ADMITTED') {
      //logger.info(`Patient ${patientID} is already ADMITTED, skipping tech issue marking`);
      await client.query('COMMIT');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'PATIENT_ALREADY_ADMITTED', null, true));
      return;
    }

    // Mark the current patient as having a tech issue and log it
    const techIssueResult = await client.query(updatePatientTechIssue, [patientID, drId]);
    logger.info(`Marked patient ${patientID} as having tech issues by doctor ${drId}`);

    // Dispatch WebSocket event to notify frontend of the tech issue update
    if (techIssueResult.rows.length > 0) {
      WebSocketManager.dispatch(TOPICS.UPDATE_PATIENT_QUEUE, techIssueResult.rows[0]);
      logger.info(`WebSocket dispatched for patient ${patientID} tech issue update`);
    }

    // Now check if doctor already has another active admission
    const activeAdmission = await client.query(activeAdmissionCheck, [drId, patientID]);
    if (activeAdmission.rows.length > 0) {
      // Doctor already has an active admission, don't admit another patient
      //logger.info(`Doctor ${drId} already has an active admission, skipping auto-admit after tech issue`);
      await client.query('COMMIT');
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      return;
    }

    // Get doctor name for logging
    const doctorQuery = Queries.getDoctorByAccessID();
    const doctorResult = await client.query(doctorQuery, [drId]);
    const doctorName = doctorResult.rows[0]?.username || drId;

    // Try to find a non-notified patient
    const result = await client.query(nonNotifiedPatient, [drId]);
    if (result.rows?.length > 0) {
      logger.info(`Doctor ${doctorName} - Admitted next patient Auto (First time): ${result.rows?.[0].patientID}`);
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', result.rows[0], true));
      return;
    } else {
      logger.info(`Doctor ${doctorName} - No non-notified patients found from assigned patient list`);

      // if everyone is already notified, let us go back to patients with tech issues
      const repeatNotification = await client.query(notifiedPatient, [drId, patientID]);
      if (repeatNotification.rows?.length > 0) {
        logger.info(
          `Doctor ${doctorName} - Admitted next patient Auto (Another chance): ${repeatNotification.rows?.[0].patientID}`,
        );

        res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', repeatNotification.rows[0], true));
        return;
      } else {
        logger.info(
          `Doctor ${doctorName} - No available patient found for a second chance from assigned patient list. Redirect Back to Online List`,
        );
        res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', null, true));
      }
    }
    await client.query('COMMIT');
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

/**
 * Get real-time patient online status directly from PatientQueue table
 * GET /doc/v1.0/patient/:id/status
 */
export const getPatientStatus: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;

  if (!patientID) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  try {
    const isOnline = await getPatientOnlineStatus(patientID);
    res.status(200).json({
      success: true,
      data: {
        patientID,
        isOnline,
      },
    });
  } catch (error) {
    logger.error(`Error getting patient status for ${patientID}:`, error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to get patient status');
  }
});

/**
 * Get real-time online status for multiple patients in batch
 * POST /doc/v1.0/patients/status
 * Body: { patientIDs: string[] }
 */
export const getBatchPatientStatus: RequestHandler = catchAll(async (req, res) => {
  const { patientIDs } = req.body;

  if (!Array.isArray(patientIDs) || patientIDs.length === 0) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'patientIDs array is required and cannot be empty');
  }

  // Validate that all patientIDs are strings
  if (!patientIDs.every((id) => typeof id === 'string')) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'All patientIDs must be strings');
  }

  try {
    const statusMap = await getBatchPatientOnlineStatus(patientIDs);
    res.status(200).json({
      success: true,
      data: statusMap,
    });
  } catch (error) {
    logger.error(`Error getting batch patient status:`, error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to get batch patient status');
  }
});

/**
 * Get detailed patient queue status information
 * GET /doc/v1.0/patient/:id/queue-status
 */
export const getPatientDetailedStatus: RequestHandler = catchAll(async (req, res) => {
  const patientID = req.params.id;

  if (!patientID) {
    throw new ApiError(httpStatus.BAD_REQUEST, 'Patient ID is required');
  }

  try {
    const queueStatus = await getPatientQueueStatus(patientID);

    if (!queueStatus) {
      res.status(200).json({
        success: true,
        data: {
          patientID,
          status: null,
          isOnline: false,
          message: 'Patient not found in queue',
        },
      });
      return;
    }

    res.status(200).json({
      success: true,
      data: {
        patientID,
        ...queueStatus,
      },
    });
  } catch (error) {
    logger.error(`Error getting detailed patient status for ${patientID}:`, error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to get detailed patient status');
  }
});

/**
 * Test endpoint to compare cached vs real-time patient status
 * GET /doc/v1.0/test/patient-status-comparison
 */
export const testPatientStatusComparison: RequestHandler = catchAll(async (_req, res) => {
  try {
    // Get cached patient data by calling the internal function
    const client = await db.connect();
    const query = Queries.fetchPatientDataLightweight(); // Use lightweight query for testing
    const result = await client.query(query);
    const cachedPatients = result.rows as PatientData[];
    client.release();

    if (!cachedPatients || cachedPatients.length === 0) {
      res.status(200).json({
        success: true,
        message: 'No patients found',
        data: {
          comparison: [],
          summary: {
            totalPatients: 0,
            discrepancies: 0,
          },
        },
      });
      return;
    }

    // Get real-time status for all patients
    const patientIDs = cachedPatients.map((p) => p.patientID);
    const realTimeStatusMap = await getBatchPatientOnlineStatus(patientIDs);

    // Compare cached vs real-time status
    const comparison = cachedPatients.map((patient) => {
      const cachedStatus = !!patient.consultation?.meetingOngoing;
      const realTimeStatus = realTimeStatusMap[patient.patientID] || false;
      const hasDiscrepancy = cachedStatus !== realTimeStatus;

      return {
        patientID: patient.patientID,
        fullName: patient.fullName,
        cachedStatus,
        realTimeStatus,
        hasDiscrepancy,
        cachedSource: 'consultation.meetingOngoing',
        realTimeSource: 'PatientQueue.status',
      };
    });

    const discrepancies = comparison.filter((c) => c.hasDiscrepancy);

    res.status(200).json({
      success: true,
      data: {
        comparison,
        summary: {
          totalPatients: comparison.length,
          discrepancies: discrepancies.length,
          discrepancyRate: `${((discrepancies.length / comparison.length) * 100).toFixed(1)}%`,
        },
        discrepancyDetails: discrepancies,
      },
    });
  } catch (error) {
    logger.error('Error in patient status comparison test:', error);
    throw new ApiError(httpStatus.INTERNAL_SERVER_ERROR, 'Failed to run patient status comparison test');
  }
});

interface PatientAssignment {
  patientID: string;
  patientName: string;
  patientEmail: string;
  slotTime: string;
  doctorName: string;
  remainingSlots: number;
}

/**
 * Assign unassigned patients to a doctor's available slots for today
 * POST /doc/v1.0/assign-patients-to-doctor
 */
export const assignUnassignedPatientsToDoctor: RequestHandler = catchAll(async (req, res) => {
  const { doctorEmail } = req.body;

  if (!doctorEmail) {
    res.status(400).send({ error: 'Doctor email is required' });
    return;
  }

  const client = await db.connect();

  try {
    await client.query('BEGIN');

    // Step 1: Get doctor's slots for today (regardless of availability)
    const doctorSlotsQuery = Queries.getDoctorSlotsToday();
    const slotsResult = await client.query(doctorSlotsQuery, [doctorEmail]);

    if (slotsResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(200).send({
        success: false,
        message: 'No slots found for this doctor today',
        data: {
          doctorEmail,
          doctorSlots: 0,
          assignedPatients: 0,
        },
      });
      return;
    }

    // Step 2: Get unassigned patients with consultations today
    const unassignedPatientsQuery = Queries.fetchUnassignedPatientsLightweight();
    const unassignedResult = await client.query(unassignedPatientsQuery);

    if (unassignedResult.rows.length === 0) {
      await client.query('ROLLBACK');
      res.status(200).send({
        success: true,
        message: 'No unassigned patients found to assign',
        data: {
          doctorEmail,
          doctorSlots: slotsResult.rows.length,
          assignedPatients: 0,
        },
      });
      return;
    }

    // Step 3: Assign patients to doctor slots
    const assignedPatients: PatientAssignment[] = [];
    const doctorSlots = slotsResult.rows;
    const unassignedPatients = unassignedResult.rows;

    // Assign each unassigned patient to the first available slot
    // If there are more patients than slots, patients will be assigned to slots cyclically
    for (let i = 0; i < unassignedPatients.length; i++) {
      const slotIndex = i % doctorSlots.length; // Cycle through slots if more patients than slots
      const slot = doctorSlots[slotIndex];
      const patient = unassignedPatients[i];

      try {
        // Create PatientSlot entry
        const createPatientSlotQuery = `
          INSERT INTO PatientSlot (patient_id, range_id, slot_id, "createdAt", "updatedAt")
          VALUES ($1, $2, $3, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
          ON CONFLICT (patient_id, range_id) DO NOTHING
          RETURNING *;
        `;

        const patientSlotResult = await client.query(createPatientSlotQuery, [
          patient.zohoID, // patient_id uses zohoID
          slot.range_id,
          slot.slot_id,
        ]);

        if (patientSlotResult.rows.length > 0) {
          assignedPatients.push({
            patientID: patient.patientID,
            patientName: patient.fullName,
            patientEmail: patient.email,
            slotTime: slot.slot_time,
            doctorName: slot.doctor_name,
            remainingSlots: 0, // Not tracking remaining slots anymore
          });

          logger.info('Successfully assigned patient to doctor slot', {
            patientID: patient.patientID,
            patientName: patient.fullName,
            doctorEmail,
            slotTime: slot.slot_time,
          });
        }
      } catch (assignmentError) {
        logger.error('Error assigning individual patient to slot', {
          patientID: patient.patientID,
          slotId: slot.slot_id,
          error: assignmentError,
        });
        // Continue with next assignment rather than failing the entire operation
      }
    }

    await client.query('COMMIT');

    res.status(200).send({
      success: true,
      message: `Successfully assigned ${assignedPatients.length} patients to doctor slots`,
      data: {
        doctorEmail,
        doctorSlots: doctorSlots.length,
        unassignedPatients: unassignedPatients.length,
        assignedPatients: assignedPatients.length,
        assignments: assignedPatients,
      },
    });
  } catch (error) {
    await client.query('ROLLBACK');
    logger.error('Error assigning patients to doctor slots:', error);
    res.status(500).send({
      success: false,
      error: 'Failed to assign patients to doctor slots',
      details: error instanceof Error ? error.message : 'Unknown error',
    });
  } finally {
    client.release();
  }
});

export const fetchAIResponses: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const limit = Math.max(1, parseInt(req.query.limit as string) || 10); // Default limit to 10, max 1 for testing
  const offset = Math.max(0, parseInt(req.query.offset as string) || 0);
  const query = `
    SELECT 
      a."lead_id", 
      a."createdAt",  
      a."condition", 
      a."risk_score",
      a."response",
      p."patientID",
      p."fullName" AS "patientName"
    FROM aicheckresponses a
    INNER JOIN patient p ON a."lead_id" = p."zohoID"
    ORDER BY a."createdAt" DESC
    LIMIT $1 OFFSET $2;
  `;
  const countQuery = `
    SELECT COUNT(*) AS total FROM aicheckresponses a
    INNER JOIN patient p ON a."lead_id" = p."zohoID";
  `;

  try {
    const { rows } = await client.query(query, [limit, offset]);
    const { rows: countRows } = await client.query(countQuery);
    res.status(200).send({
      data: rows,
      pagination: {
        total: countRows[0].total,
        limit,
        offset,
        hasMore: offset + limit < countRows[0].total,
      },
    });
  } catch (e) {
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const fetchAIResponseById: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const patientID = req.params.id;
  const source = req.query.source as string;

  if (!patientID) {
    res.status(400).send({
      success: false,
      message: 'Patient ID is required',
    });
    return;
  }
  let query;
  if (source === 'zoho') {
    query = `
      SELECT a.*, p."fullName" AS "patientName"
      FROM aicheckresponses a
      INNER JOIN patient p ON a."lead_id" = p."zohoID"
      WHERE p."zohoID" = $1
      ORDER BY a."createdAt" DESC;
  `;
  } else {
    query = `
    SELECT a.*, p."fullName" AS "patientName"
    FROM aicheckresponses a
    INNER JOIN patient p ON a."lead_id" = p."zohoID"
    WHERE p."patientID" = $1
    ORDER BY a."createdAt" DESC;
`;
  }

  try {
    const { rows } = await client.query(query, [patientID]);
    res.status(200).send(rows);
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as AxiosError;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});
export const updateAnnouncement: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.body.id as string;
  try {
    await client.query('BEGIN');

    if (id.trim().length <= 0) {
      // ID is absent
      // POST

      await client.query(`INSERT INTO  announcement (title, announcement) VALUES($1, $2)`, [
        req.body.title,
        req.body.announcement,
      ]);
      res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
      await client.query('COMMIT');

      return;
    }

    // ID is present
    // PUT

    await client.query(`UPDATE announcement SET title = $2, announcement=$3 WHERE id = $1`, [
      id,
      req.body.title,
      req.body.announcement,
    ]);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
    await client.query('COMMIT');
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const retrieveAnnouncement: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id as string;

  const query = `
  SELECT 
    a.id,
    a.title,
    a.announcement,
    a."createdAt",
    a."updatedAt"
FROM 
    public.announcement a
LEFT JOIN 
    public.announcement_dr ad 
    ON a.id = ad."announcementId"
    AND ad."drId" = $1
WHERE 
    (ad.confirmed IS NULL OR ad.confirmed = FALSE) AND a.active = TRUE
ORDER BY 
    a."updatedAt" DESC;`;

  try {
    const response = await client.query(query, [id]);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', response.rows, true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const deleteAnnouncement: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const id = req.params.id as string;
  const query = `UPDATE announcement SET active = FALSE WHERE id = $1`;
  try {
    const response = await client.query(query, [id]);
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', response.rows, true));
  } catch (e) {
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});

export const confirmAnnouncement: RequestHandler = catchAll(async (req, res) => {
  const client = await db.connect();
  const drId = req.body.drId as string;
  const announcementId = req.body.announcementId.trim() as string;

  const query = `INSERT INTO announcement_dr ("drId", "announcementId", confirmed)
                SELECT $1, $2, $3
                WHERE EXISTS (
                    SELECT 1 FROM Dr WHERE "accessID" = $1
                ) AND EXISTS (
                    SELECT 1 FROM "announcement" WHERE id = $2
                )`;
  try {
    await client.query('BEGIN');
    await client.query(query, [drId, announcementId, true]);
    await client.query('COMMIT');
    res.status(200).send(new ApiResponse(httpStatus.OK, 'SUCCESS', [], true));
    return;
  } catch (e) {
    await client.query('ROLLBACK');
    const error = e as Error;
    throw new ApiError(httpStatus.BAD_REQUEST, error.message);
  } finally {
    client.release();
  }
});
