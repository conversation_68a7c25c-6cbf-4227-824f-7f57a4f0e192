import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  Chip,
  Button,
  CircularProgress,
  ToggleButton,
  ToggleButtonGroup,
  Pagination
} from '@mui/material';
import {
  CheckCircle as ApproveIcon,
  Cancel as RejectIcon,
  Visibility as ViewIcon,
  TrendingUp as IncreaseIcon,
  Schedule as ExtendIcon,
  Add as AddIcon,
  ExpandMore as QuantityIcon
} from '@mui/icons-material';
import { DateTime } from 'luxon';
import adminRequestsService, { AdminRequest, PaginationInfo } from '../../../services/admin-requests.service';
import RequestDetailsModal from './RequestDetailsModal';

interface ProcessedRequestsTabProps {
  onStatsUpdate: () => void;
  onShowSnackbar: (message: string, severity: 'success' | 'error' | 'info' | 'warning') => void;
}

const ProcessedRequestsTab: React.FC<ProcessedRequestsTabProps> = ({ onShowSnackbar }) => {
  const [requests, setRequests] = useState<AdminRequest[]>([]);
  const [loading, setLoading] = useState(true);
  const [statusFilter, setStatusFilter] = useState<'both' | 'approved' | 'rejected'>('both');
  const [typeFilter, setTypeFilter] = useState<'both' | 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase'>('both');
  const [selectedRequest, setSelectedRequest] = useState<AdminRequest | null>(null);
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [pagination, setPagination] = useState<PaginationInfo>({
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false
  });

  useEffect(() => {
    loadProcessedRequests();
  }, [statusFilter, typeFilter, pagination.page]);

  const loadProcessedRequests = async () => {
    try {
      setLoading(true);
      const status = statusFilter === 'both' ? undefined : statusFilter;
      const type = typeFilter === 'both' ? undefined : typeFilter;

      const response = await adminRequestsService.getProcessedRequests(
        status as 'approved' | 'rejected' | undefined,
        type as 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase' | undefined,
        pagination.limit,
        pagination.page
      );

      setRequests(response.requests || []);
      setPagination(response.pagination || {
        page: 1,
        limit: 20,
        total: 0,
        totalPages: 0,
        hasNext: false,
        hasPrev: false
      });
    } catch (error) {
      console.error('Error loading processed requests:', error);
      onShowSnackbar('Failed to load processed requests', 'error');
      setRequests([]);
    } finally {
      setLoading(false);
    }
  };

  const handleStatusFilterChange = (
    _event: React.MouseEvent<HTMLElement>,
    newFilter: 'both' | 'approved' | 'rejected'
  ) => {
    if (newFilter !== null) {
      setStatusFilter(newFilter);
      setPagination(prev => ({ ...prev, page: 1 }));
    }
  };

  const handleTypeFilterChange = (
    _event: React.MouseEvent<HTMLElement>,
    newFilter: 'both' | 'thc_increase' | 'extend_tp' | 'add_22_thc' | 'quantity_increase'
  ) => {
    if (newFilter !== null) {
      setTypeFilter(newFilter);
      setPagination(prev => ({ ...prev, page: 1 }));
    }
  };

  const handlePageChange = (_event: React.ChangeEvent<unknown>, page: number) => {
    setPagination(prev => ({ ...prev, page }));
  };

  const formatTimestamp = (timestamp: string) => {
    return DateTime.fromISO(timestamp)
      .setZone('Australia/Sydney')
      .toFormat('dd/MM/yyyy HH:mm');
  };

  const getRequestTypeIcon = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return <IncreaseIcon sx={{ color: '#ff9800' }} />;
      case 'extend_tp':
        return <ExtendIcon sx={{ color: '#2196f3' }} />;
      case 'add_22_thc':
        return <AddIcon sx={{ color: '#4caf50' }} />;
      case 'quantity_increase':
        return <QuantityIcon sx={{ color: '#9c27b0' }} />;
      default:
        return <ViewIcon sx={{ color: '#757575' }} />;
    }
  };

  const getRequestTypeLabel = (type: string) => {
    switch (type) {
      case 'thc_increase':
        return 'THC Increase';
      case 'extend_tp':
        return 'Extend TP';
      case 'add_22_thc':
        return 'Add 22% THC';
      case 'quantity_increase':
        return 'Quantity Increase';
      default:
        return 'Unknown Request';
    }
  };

  const getRiskScoreColor = (score: number, maxScore: number) => {
    const percentage = (score / maxScore) * 100;
    if (percentage >= 70) return '#4caf50'; // Green
    if (percentage >= 40) return '#ff9800'; // Orange
    return '#f44336'; // Red
  };

  const getQuestionsSummary = (questionnaireData: any) => {
    if (!questionnaireData) return null;

    // Parse questionnaire data if it's a string
    const data = typeof questionnaireData === 'string' ?
      JSON.parse(questionnaireData) : questionnaireData;

    // Handle different data structures
    const responses = Array.isArray(data) ? data : (data?.responses || []);

    if (responses.length === 0) return null;

    const scoredQuestions = responses.filter((r: any) => r.score > 0).length;
    const totalQuestions = responses.length;

    return { scoredQuestions, totalQuestions };
  };

  const handleViewDetails = (request: AdminRequest) => {
    setSelectedRequest(request);
    setDetailsModalOpen(true);
  };

  const handleCloseDetails = () => {
    setDetailsModalOpen(false);
    setSelectedRequest(null);
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {/* Header and Filters */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h6">
          Processed Requests ({requests.length})
        </Typography>

        <Box sx={{ display: 'flex', gap: 2 }}>
          {/* Status Filter */}
          <ToggleButtonGroup
            value={statusFilter}
            exclusive
            onChange={handleStatusFilterChange}
            aria-label="status filter"
            size="small"
          >
            <ToggleButton value="both" aria-label="show both">
              Both
            </ToggleButton>
            <ToggleButton value="approved" aria-label="show approved" sx={{
              '&.Mui-selected': {
                backgroundColor: '#008000',
                color: 'white',
                '&:hover': {
                  backgroundColor: '#006600',
                }
              }
            }}>
              <ApproveIcon sx={{ mr: 1, fontSize: 16 }} />
              Approved
            </ToggleButton>
            <ToggleButton value="rejected" aria-label="show rejected">
              <RejectIcon sx={{ mr: 1, fontSize: 16 }} />
              Rejected
            </ToggleButton>
          </ToggleButtonGroup>

          {/* Type Filter */}
          <ToggleButtonGroup
            value={typeFilter}
            exclusive
            onChange={handleTypeFilterChange}
            aria-label="type filter"
            size="small"
          >
            <ToggleButton value="both" aria-label="show all types">
              All Types
            </ToggleButton>
            <ToggleButton value="thc_increase" aria-label="show thc increase">
              <IncreaseIcon sx={{ mr: 1, fontSize: 16 }} />
              THC Increase
            </ToggleButton>
            <ToggleButton value="extend_tp" aria-label="show extend tp">
              <ExtendIcon sx={{ mr: 1, fontSize: 16 }} />
              Extend TP
            </ToggleButton>
            <ToggleButton value="add_22_thc" aria-label="show add 22% thc">
              <AddIcon sx={{ mr: 1, fontSize: 16 }} />
              Add 22% THC
            </ToggleButton>
            <ToggleButton value="quantity_increase" aria-label="show quantity increase">
              <QuantityIcon sx={{ mr: 1, fontSize: 16 }} />
              Quantity Increase
            </ToggleButton>
          </ToggleButtonGroup>
        </Box>
      </Box>

      {/* Requests Table */}
      {requests.length === 0 ? (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="textSecondary">
            No processed requests found
          </Typography>
          <Typography variant="body2" color="textSecondary">
            Try adjusting your filters
          </Typography>
        </Box>
      ) : (
        <>
          <TableContainer component={Paper}>
            <Table>
              <TableHead>
                <TableRow>
                  <TableCell>Patient</TableCell>
                  <TableCell>Request Type</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell>Risk Score</TableCell>
                  <TableCell>Questions Summary</TableCell>
                  <TableCell>Submitted</TableCell>
                  <TableCell>Processed</TableCell>
                  <TableCell>Doctor</TableCell>
                  <TableCell>Notes</TableCell>
                  <TableCell>Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {requests.map((request) => (
                  <TableRow key={request.id} hover>
                    <TableCell>
                      <Box>
                        <Typography variant="body2" fontWeight="bold">
                          {request.patient_name}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          ID: {request.patient_id}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        {getRequestTypeIcon(request.type)}
                        <Typography variant="body2">
                          {getRequestTypeLabel(request.type)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      <Chip
                        icon={request.status === 'approved' ? <ApproveIcon /> : <RejectIcon />}
                        label={request.status.toUpperCase()}
                        size="small"
                        sx={{
                          backgroundColor: request.status === 'approved' ? '#008000' : undefined,
                          color: request.status === 'approved' ? 'white' : undefined,
                          '& .MuiChip-icon': {
                            color: request.status === 'approved' ? 'white' : undefined
                          }
                        }}
                        color={request.status === 'approved' ? undefined : 'error'}
                      />
                    </TableCell>
                    <TableCell>
                      <Chip
                        label={`${request.total_score}/${request.max_score}`}
                        size="small"
                        sx={{
                          backgroundColor: getRiskScoreColor(request.total_score, request.max_score),
                          color: 'white',
                          fontWeight: 'bold'
                        }}
                      />
                    </TableCell>
                    <TableCell>
                      {(() => {
                        const summary = getQuestionsSummary(request.questionnaire_data);
                        return summary ? (
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Chip
                              label={`${summary.scoredQuestions}/${summary.totalQuestions} scored`}
                              size="small"
                              variant="outlined"
                              sx={{ fontSize: '0.75rem' }}
                            />
                          </Box>
                        ) : (
                          <Typography variant="body2" color="textSecondary">
                            No data
                          </Typography>
                        );
                      })()}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatTimestamp(request.created_at)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {request.reviewed_at ? formatTimestamp(request.reviewed_at) : 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {request.doctor_name || 'N/A'}
                      </Typography>
                    </TableCell>
                    <TableCell sx={{ maxWidth: 200 }}>
                      <Typography
                        variant="body2"
                        sx={{
                          overflow: 'hidden',
                          textOverflow: 'ellipsis',
                          whiteSpace: 'nowrap',
                          color: request.status === 'rejected' ? 'error.main' : 'text.secondary'
                        }}
                        title={request.review_notes || ''}
                      >
                        {request.review_notes || (request.status === 'approved' ? 'Approved via messenger' : 'N/A')}
                      </Typography>
                    </TableCell>

                    <TableCell>
                      <Button
                        size="small"
                        startIcon={<ViewIcon />}
                        variant="outlined"
                        sx={{ minWidth: 100 }}
                        onClick={() => handleViewDetails(request)}
                      >
                        View Details
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          {/* Pagination */}
          {!loading && requests.length > 0 && (
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 3 }}>
              <Typography variant="body2" color="textSecondary">
                Showing {((pagination.page - 1) * pagination.limit) + 1} to{' '}
                {Math.min(pagination.page * pagination.limit, pagination.total)} of{' '}
                {pagination.total} processed requests
              </Typography>
              <Pagination
                count={Math.max(pagination.totalPages, 1)}
                page={pagination.page}
                onChange={handlePageChange}
                color="primary"
                showFirstButton
                showLastButton
                size="medium"
                disabled={pagination.totalPages <= 1}
              />
            </Box>
          )}
        </>
      )}

      {/* Request Details Modal */}
      <RequestDetailsModal
        open={detailsModalOpen}
        request={selectedRequest}
        onClose={handleCloseDetails}
      />
    </Box>
  );
};

export default ProcessedRequestsTab;